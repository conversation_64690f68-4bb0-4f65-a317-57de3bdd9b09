package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.compensation.model.baggage.accident.dto.CompensationExpressInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.*;
import com.swcares.aps.compensation.model.compensation.vo.*;
import com.swcares.aps.compensation.model.complaint.vo.CompensationTypeVo;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationOrderInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationPaxFrozenDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.FreezeOrderPaxDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationFromDetailsVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoExamineVO;
import com.swcares.aps.compensation.model.overbook.vo.OverBookConfigVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.compensation.remote.api.irregularflight.ComplaintAccidentApi;
import com.swcares.aps.staff.bff.service.CompensationOrderService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName：CompensationOrderCommandsController
 * @Description：补偿单命令类接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 10:44
 * @version： v1.0
 */

@RestController
@RequestMapping("/h5/compensation")
@Api(tags = "补偿单接口")
@ApiVersion(value = "补偿单接口v1.0.1")
@Slf4j
public class CompensationOrderController extends BaseController {

    private CompensationOrderService compensationOrderService;
    private CompensationInfoApi compensationInfoApi;
    private ComplaintAccidentApi complaintAccidentApi;
    @Autowired
    public CompensationOrderController(CompensationOrderService compensationOrderService,CompensationInfoApi compensationInfoApi,ComplaintAccidentApi complaintAccidentApi){
        this.compensationOrderService=compensationOrderService;
        this.compensationInfoApi = compensationInfoApi;
        this.complaintAccidentApi = complaintAccidentApi;
    }

    /**
     * @title addMaterialCompensation
     * @description 创建实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @PostMapping("/addMaterialCompensation")
    @ApiOperation(value = "创建实物补偿单接口")
    public BaseResult<String> addMaterialCompensation(@RequestBody CompensationMaterialAddCommandDTO request){
        return compensationOrderService.addMaterialCompensation(request);
    }

    /**
     * @title editMaterialCompensation
     * @description 编辑实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @PostMapping("/editMaterialCompensation")
    @ApiOperation(value = "编辑实物补偿单接口")
    public BaseResult<String> editMaterialCompensation(@RequestBody CompensationMaterialEditCommandDTO request){
        return compensationOrderService.editMaterialCompensation(request);
    }

    /**
     * @title addCashCompensation
     * @description 创建现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @PostMapping("/addCashCompensation")
    @ApiOperation(value = "创建现金补偿单接口")
    public BaseResult<String> addCashCompensation(@RequestBody CompensationCashAddCommandDTO request){
        return compensationOrderService.addCashCompensation(request);
    }

    /**
     * @title editCashCompensation
     * @description 编辑现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @PostMapping("/editCashCompensation")
    @ApiOperation(value = "编辑现金补偿单接口")
    public BaseResult<String> editCashCompensation(@RequestBody CompensationCashEditCommandDTO request){
        return compensationOrderService.editCashCompensation(request);
    }

    /**
     * @title submit
     * @description 提交补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:29
     * @param orderId
     * @return
     */
    @PostMapping("submit/{orderId}")
    @ApiOperation(value = "提交补偿单接口")
    public BaseResult<CompensationAuditOperationVO> submit(@PathVariable("orderId") String orderId){
        return compensationOrderService.submit(orderId);
    }

    /**
     * @title delete
     * @description 删除补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:30
     * @param orderId
     * @return
     */
    @GetMapping("delete")
    @ApiOperation(value = "删除补偿单接口")
    public BaseResult<String> delete(@ApiParam(value = "赔偿单id",required = true) String orderId){
        return compensationOrderService.delete(orderId);
    }

    /**
     * @title compensationBaseInfoPage
     * @description 条件分页查询赔偿单最基本信息记录
     * <AUTHOR>
     * @date 2022/3/16 9:28
     * @param request
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询赔偿单最基本信息记录")
    public PagedResult<List<CompensationBaseInfoVO>> compensationBaseInfoPage(@RequestBody CompensationBaseInfoRequestDTO request) {
        return compensationOrderService.compensationBaseInfoPage(request);
    }

    @PostMapping("manager/page")
    @ApiOperation(value = "条件分页查询赔偿单信息记录")
    public PagedResult<List<CompensationOrderInfoExamineVO>> pages(@RequestBody CompensationOrderInfoPagedDTO dto) {
        return compensationInfoApi.pages(dto);
    }

    @GetMapping("manager/detail")
    @ApiOperation(value = "补偿单详情")
    public BaseResult<CompensationFromDetailsVO> detail(@ApiParam(value = "赔偿单主键id", required = true) Long id) {
        return ok(compensationOrderService.getCompensationDetailsVo(id));
    }

    @GetMapping("manager/findChoicePax")
    @ApiOperation(value = "通过赔偿单ID、及筛选项，查询已选旅客列表")
    public BaseResult<List<CompensationChoicePaxVO>> findChoicePax(CompensationPaxFrozenDTO compensationPaxFrozenDTO) {
        return compensationInfoApi.findChoicePax(compensationPaxFrozenDTO);
    }
    
    /**
     * @title compensationDetailSearch
     * @description 条件查询补偿单详情信息
     * <AUTHOR>
     * @date 2022/3/16 9:28
     * @param orderId
     * @return
     */
    @GetMapping("/detail/{orderId}")
    @ApiOperation(value = "现金补偿单详情信息")
    public BaseResult<Object> compensationDetailSearch(@PathVariable String  orderId){
        return compensationOrderService.compensationDetailSearch(orderId);
    }

    /**
     * @title findCompensationLuggageList
     * @description 箱包补偿单列表查询
     * <AUTHOR>
     * @date 2022/4/15 16:27
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialQueriesListVO>>
     */
    @PostMapping("/findCompensationLuggageList")
    @ApiOperation(value = "箱包补偿单列表查询")
    public PagedResult<List<CompensationMaterialQueriesListVO>> findCompensationLuggageList(@RequestBody CompensationMaterialListDTO dto ){
        return compensationOrderService.findCompensationLuggageList(dto);
    }

    /**
     * @title findCompensationLuggageDetailInfo
     * @description 箱包补偿单详情信息
     * <AUTHOR>
     * @date 2022/4/15 16:28
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailFinalVO>
     */
    @PostMapping("/findCompensationLuggageDetailInfo")
    @ApiOperation(value = "箱包补偿单详情信息")
    public BaseResult<CompensationMaterialDetailFinalVO> findCompensationLuggageDetailInfo(@RequestBody @Valid CompensationMaterialDetailDTO dto)  {
        return compensationOrderService.findCompensationLuggageDetailInfo(dto);
    }





    /**
     * @title auditPassToTakeEffect
     * @description 审核通过 确认发放接口
     * <AUTHOR>
     * @date 2022/4/14 16:24
     * @param orderId
     * @return BaseResult<String>
     */
    @GetMapping("/auditPassToTakeEffect")
    @ApiOperation(value = "(确认发放)审核通过转生效接口")
    public BaseResult<String> auditPassToTakeEffect(@ApiParam(value = "赔偿单id",required = true) String orderId){
        return compensationOrderService.auditPassToTakeEffect(orderId);
    }

    /**
     * @title closeCompensation
     * @description @TODO
     * <AUTHOR>
     * @date 2022/4/14 16:42
     * @param orderId
     * @return BaseResult<String>
     */
    @GetMapping("/close")
    @ApiOperation(value = "关闭补偿单接口")
    public BaseResult<String> close(@ApiParam(value = "赔偿单id",required = true) String orderId){
        return compensationOrderService.closeCompensation(orderId);
    }

    /**
     * @title findCashCompensationList
     * @description 查询现金赔偿单列表信息
     * <AUTHOR>
     * @date 2022/4/21 16:51
     * @param request
     * @return PageResult<Object>
     */
    @PostMapping("/findCashCompensationList")
    @ApiOperation(value = "查询现金赔偿单列表信息")
    public PagedResult<List<Object>> findCashCompensationList(@RequestBody CompensationBaseInfoRequestDTO request){
        return compensationOrderService.findCashCompensationList(request);
    }


    /**
     * @title freezeOrderPax
     * @description 冻结 || 解冻旅客
     * <AUTHOR>
     * @date 2022/4/21 17:04
     * @param freezeOrderPaxDTO
     * @return BaseResult<Object>
     */
    @PostMapping("/freezeOrderPax")
    @ApiOperation(value = "冻结 || 解冻旅客")
    public BaseResult<Object> freezeOrderPax(@RequestBody FreezeOrderPaxDTO freezeOrderPaxDTO){
        return compensationOrderService.freezeOrderPax(freezeOrderPaxDTO);
    }


    /**
     * @title mailGrant
     * @description 邮寄发放
     * <AUTHOR>
     * @date 2022/5/7 9:24
     * @param compensationExpressInfoDTO
     * @return BaseResult<Object>
     */
    @PostMapping("mailGrant")
    @ApiOperation(value = "邮寄发放")
    public BaseResult<Object> mailGrant(@RequestBody CompensationExpressInfoDTO compensationExpressInfoDTO){
        return compensationOrderService.mailGrant(compensationExpressInfoDTO);
    }

    /**
     * @title mailGrant
     * @description 线下发放
     * <AUTHOR>
     * @date 2022/5/7 9:24
     * @param offlineGrantDTO
     * @return BaseResult<Object>
     */
    @PostMapping("offlineGrant")
    @ApiOperation(value = "线下发放")
    public BaseResult<Object> offlineGrant(@RequestBody OfflineGrantDTO offlineGrantDTO){
        return compensationOrderService.offlineGrant(offlineGrantDTO);
    }


    @ApiImplicitParam(name = "belongAirline",value = "归属航司",required = true)
    @GetMapping("/getBaggageCompensationAmount")
    @ApiOperation(value = "异常行李补偿单-标准补偿金额")
    public BaseResult<List<BaggageCompensationAmountRuleVO>> getBaggageCompensationAmount(String belongAirline){
        List<BaggageCompensationAmountRuleVO> amountRuleVOList = new ArrayList<>();
        BaggageCompensationAmountRuleVO amountRuleVO = new BaggageCompensationAmountRuleVO();
        amountRuleVO.setType("21");
        amountRuleVO.setStandardAmount("200");
        BaggageCompensationAmountRuleVO amountRuleVO1 = new BaggageCompensationAmountRuleVO();
        amountRuleVO1.setType("22");
        amountRuleVO1.setStandardAmount("300");

        BaggageCompensationAmountRuleVO amountRuleVO2 = new BaggageCompensationAmountRuleVO();
        amountRuleVO2.setType("24");
        amountRuleVO2.setStandardAmount("400");
        BaggageCompensationAmountRuleVO amountRuleVO3 = new BaggageCompensationAmountRuleVO();
        amountRuleVO3.setType("25");
        amountRuleVO3.setStandardAmount("400");
        amountRuleVOList.add(amountRuleVO);
        amountRuleVOList.add(amountRuleVO1);
        amountRuleVOList.add(amountRuleVO2);
        amountRuleVOList.add(amountRuleVO3);
        // TODO:补偿金额的规则，展示没有设计，经和夏阳讨论，目前先返回写死的值，后续做赔偿金额规则时获取
        return ok(amountRuleVOList);
    }

    @ApiImplicitParam(name = "belongAirline",value = "归属航司",required = true)
    @GetMapping("/getCompensationType")
    @ApiOperation(value = "获取补偿方式")
    public BaseResult<List<CompensationTypeVo>> getCompensationType(String belongAirline, String accidentType){
        return complaintAccidentApi.getCompensationType(belongAirline,accidentType);
    }
}
