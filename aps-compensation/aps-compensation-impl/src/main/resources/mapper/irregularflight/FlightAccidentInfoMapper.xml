<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.irregularflight.mapper.FlightAccidentInfoMapper">

    <select id="page" resultType="com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoVO" databaseId="oracle">
        select
        fo.id	,fo.accident_no,	fo.flight_no,	fo.flight_date,	fo.segment	,
        fo.std	, fo.etd	,fo.delay_interval,	fo.late_reason,fo.created_time,
        fo.tovoid_time,	fo.updated_time,		fo.choice_segment_ch, fo.ACCIDENT_SOURCE,
        fo.accident_status accidentStatus,
        fo.accident_type accidentType,
        fo.fc_type fcType,
        fo.fc_type_owner fcTypeOwner,
        get_compensation_count(fo.id) compensationNum,
        fo.created_by createdBy,
        fo.updated_by updated_by,
        (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER)
        FROM UC_USER uc
        LEFT JOIN UC_EMPLOYEE ue
        ON uc.EMPLOYEE_ID  = ue.id
        WHERE to_char(uc.ID) = fo.TOVOID_ID) tovoidId
        FROM flight_accident_info fo
        where 1=1
        <if test="dto.accidentNo != null and dto.accidentNo != ''">
            and  fo.accident_no = #{dto.accidentNo}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and  fo.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.accidentSource != null and dto.accidentSource != ''">
            and  fo.ACCIDENT_SOURCE = #{dto.accidentSource}
        </if>
        <if test="dto.accidentStatus != null and dto.accidentStatus.size()>0">
            and fo.accident_status in
            <foreach collection="dto.accidentStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.fcType != null and dto.fcType != ''">
            and  #{dto.fcType} = fo.fc_type
        </if>
        <if test="dto.fcTypeOwner != null and dto.fcTypeOwner != ''">
            and  #{dto.fcTypeOwner} = fo.fc_type_owner
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
            and  fo.flight_date  BETWEEN #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.orgCity != null and dto.orgCity != ''">
            and  fo.segment like concat(concat('%',#{dto.orgCity}),'-%')
        </if>
        <if test="dto.dstCity != null and dto.dstCity != ''">
            and  fo.segment like concat(concat('%-',#{dto.dstCity}),'%')
        </if>
        <!--           行权限  -->
        <if test="dto.accidentType != null and dto.accidentType.size()>0">
            and  fo.accident_type in(
            select sda.dict_item from sys_dictionary_data sda where  sda.item_value in
            <foreach collection="dto.accidentType" item="type"  open="(" separator="," close=")">
                #{type}
            </foreach>
            )
        </if>
        <if test="dto.workStations != null and dto.workStations.size()>0">
            <foreach collection="dto.workStations" item="station"  index="i" open="and(" separator="or" close=")">
                instr(fo.segment,#{station})
            </foreach>
        </if>
        order by fo.flight_date desc ,fo.std,fo.accident_status asc
    </select>

    <select id="page" resultType="com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoVO" databaseId="mysql">
        select
        fo.id	,fo.accident_no,	fo.flight_no,	fo.flight_date,	fo.segment	,
        fo.std	, fo.etd	,fo.delay_interval,	fo.late_reason,fo.created_time,
        fo.tovoid_time,	fo.updated_time,		fo.choice_segment_ch,
        fo.accident_status accidentStatus,
        fo.accident_type accidentType,
        fo.fc_type fcType,
        fo.fc_type_owner fcTypeOwner,
        get_compensation_count(fo.id) compensationNum,
        (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER)
        FROM UC_USER uc
        LEFT JOIN UC_EMPLOYEE ue
        ON uc.EMPLOYEE_ID  = ue.id
        WHERE uc.ID = fo.created_by) createdBy,
        (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER)
        FROM UC_USER uc
        LEFT JOIN UC_EMPLOYEE ue
        ON uc.EMPLOYEE_ID  = ue.id
        WHERE uc.ID = fo.updated_by) updated_by,
        (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER)
        FROM UC_USER uc
        LEFT JOIN UC_EMPLOYEE ue
        ON uc.EMPLOYEE_ID  = ue.id
        WHERE uc.ID = fo.TOVOID_ID) tovoidId
        FROM flight_accident_info fo
        where 1=1
        <if test="dto.accidentNo != null and dto.accidentNo != ''">
            and  fo.accident_no = #{dto.accidentNo}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and  fo.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.accidentStatus != null and dto.accidentStatus.size()>0">
            and fo.accident_status in
            <foreach collection="dto.accidentStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.fcType != null and dto.fcType != ''">
            and  #{dto.fcType} = fo.fc_type
        </if>
        <if test="dto.fcTypeOwner != null and dto.fcTypeOwner != ''">
            and  #{dto.fcTypeOwner} = fo.fc_type_owner
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
            and  fo.flight_date  BETWEEN #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.orgCity != null and dto.orgCity != ''">
            and  fo.segment like concat(concat('%',#{dto.orgCity}),'-%')
        </if>
        <if test="dto.dstCity != null and dto.dstCity != ''">
            and  fo.segment like concat(concat('%-',#{dto.dstCity}),'%')
        </if>
        <!--           行权限  -->
        <if test="dto.accidentType != null and dto.accidentType.size()>0">
            and  fo.accident_type in(
            select sda.dict_item from sys_dictionary_data sda where  sda.item_value in
            <foreach collection="dto.accidentType" item="type"  open="(" separator="," close=")">
                #{type}
            </foreach>
            )
        </if>
        <if test="dto.workStations != null and dto.workStations.size()>0">
            <foreach collection="dto.workStations" item="station"  index="i" open="and(" separator="or" close=")">
                instr(fo.segment,#{station})
            </foreach>
        </if>
        order by fo.flight_date desc ,fo.std,fo.accident_status asc
    </select>

    <select id="findFilghtExistsAccident" resultType="com.swcares.aps.compensation.model.irregularflight.vo.FlightExistsAccidentVO">
        select
        t.id,t.accident_no,
        t.accident_status as accidentStatus,
        t.fc_type as fcType,
        t.choice_segment_ch,
        t.created_by
        from flight_accident_info t
        where t.accident_status != '4' and t.flight_no = #{flightNo} and t.flight_date = #{date}
        <if test="id != null ">
            and t.id != #{id}
        </if>
    </select>

    <select id="getCompensationOrderById" parameterType="java.lang.Long" resultType="com.swcares.aps.compensation.model.irregularflight.vo.AccidentCompensationOrderVO" databaseId="oracle">
        select
            oi.order_no,
            oi.id as orderId,
            max(get_cityNameAnd3Code(oi.service_city)) serviceCity,
            COUNT(decode(pi.receive_status, 1,'true',null))
            + COUNT(decode(pi.receive_status,1,decode(pi.with_baby,'1','true',null),null))
            actualCarryOutNum,
            COUNT(DISTINCT pi.pax_id) +COUNT(decode(pi.with_baby,'1','true',null))  as planCarryOutNum,
            COUNT(decode(pi.switch_off,1,'true',null)) as frozenNum,
            SUM(decode(pi.receive_status, 1,current_amount,0)) actualCompensateMoney,
            max(oi.sum_money) as planCompensateMoney ,
            max(oi.compensate_type) compensateType,
            max(oi.status) status,
            max(oi.created_by) applyUser,
            max(oi.created_time) applyDate
        FROM compensation_order_info oi
        LEFT JOIN compensation_pax_info pi on oi.id = pi.order_id
        WHERE oi.accident_id = #{id}
        GROUP BY oi.order_no,oi.service_city,oi.id
        ORDER BY oi.order_no asc
    </select>

    <select id="getCompensationOrderById" parameterType="java.lang.Long" resultType="com.swcares.aps.compensation.model.irregularflight.vo.AccidentCompensationOrderVO" databaseId="mysql">
        select
            oi.order_no,
            oi.id as orderId,
            max(get_cityNameAnd3Code(oi.service_city)) serviceCity,
            COUNT(if(pi.receive_status = 1,true,null))
            + COUNT(if((pi.receive_status = 1 and pi.with_baby='1'),true,null))
            actualCarryOutNum,
            COUNT(DISTINCT pi.pax_id) +COUNT(if(pi.with_baby='1',true,null))  as planCarryOutNum,
            COUNT(if(pi.switch_off=1,true,null)) as frozenNum,
            SUM(if(pi.receive_status = 1,current_amount,0)) actualCompensateMoney,
            max(oi.sum_money) as planCompensateMoney ,
            max(oi.compensate_type) compensateType,
            max(oi.status) status,
            max(concat(e.name,e.job_number)) applyUser,
            max(oi.created_time) applyDate
        FROM compensation_order_info oi
        LEFT JOIN compensation_pax_info pi on oi.id = pi.order_id
        LEFT JOIN uc_user u ON u.id = oi.created_by
        LEFT JOIN uc_employee e ON u.employee_id = e.id
        WHERE oi.accident_id = #{id}
        GROUP BY oi.order_no,oi.service_city,oi.id
        ORDER BY oi.order_no asc
    </select>


    <select id="findById" parameterType="java.lang.Long" resultType="com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO" databaseId="oracle">
        select
            fo.id	,fo.accident_no,	fo.flight_no,	fo.flight_date,	fo.segment,	fo.BELONG_AIRLINE as belongAirline,
            fo.std	, fo.etd	,fo.delay_interval,	fo.late_reason,fo.created_time,
            fo.tovoid_time,fo.updated_time,		fo.choice_segment_ch,
            fo.accident_status accidentStatus,
            fo.ACCIDENT_SOURCE accidentSource,
            fo.accident_type accidentType,
            fo.fc_type fcType,
            fo.fc_type_owner fcTypeOwner,
            fo.atd,
            fo.created_by createName,
            fo.updated_by updated_by,
            fo.TOVOID_ID toVoidName
        FROM flight_accident_info fo
        where 1 = 1
        and fo.id = #{id}
    </select>

    <select id="findById" parameterType="java.lang.Long" resultType="com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO" databaseId="mysql">
        select
            fo.id	,fo.accident_no,	fo.flight_no,	fo.flight_date,	fo.segment	,
            fo.std	, fo.etd	,fo.delay_interval,	fo.late_reason,fo.created_time,
            fo.tovoid_time,	fo.updated_time,		fo.choice_segment_ch,
            fo.accident_status accidentStatus,
            fo.accident_type accidentType,
            fo.fc_type fcType,
            fo.fc_type_owner fcTypeOwner,
            fo.atd,
            (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER)
             FROM UC_USER uc
             LEFT JOIN UC_EMPLOYEE ue
             ON uc.EMPLOYEE_ID  = ue.id
             WHERE uc.ID = fo.created_by) createName,
            (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER)
             FROM UC_USER uc
             LEFT JOIN UC_EMPLOYEE ue
             ON uc.EMPLOYEE_ID  = ue.id
             WHERE uc.ID = fo.updated_by) updated_by,
            (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER)
             FROM UC_USER uc
             LEFT JOIN UC_EMPLOYEE ue
             ON uc.EMPLOYEE_ID  = ue.id
             WHERE uc.ID = fo.TOVOID_ID) toVoidName
        FROM flight_accident_info fo
        LEFT JOIN uc_user u ON u.id = fo.created_by
        LEFT JOIN uc_employee e ON u.employee_id = e.id
        where 1 = 1
        and fo.id = #{id}
    </select>


    <select id="findFlightCompensationInfo" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationFlightInfoVO">
        SELECT
            fai.ID flightId,
            fai.FLIGHT_NO flightNo,
            fai.FLIGHT_DATE flightDate,
            fai.SEGMENT segment,
            fai.CHOICE_SEGMENT_CH segmentCh,
            fai.STD ,
            fai.etd,
            fai.LATE_REASON
        FROM FLIGHT_ACCIDENT_INFO fai
        WHERE id = #{id}
    </select>


    <!--    <select id="findChoiceSegment" resultType="java.util.HashMap" databaseId="mysql">-->
<!--        SELECT substring_index(substring_index(a.choice_segment_ch,',',b.help_topic_id+1),',',-1) segmentChKey,-->
<!--               substring_index(substring_index(a.segment,',',b.help_topic_id+1),',',-1) segmentChValue-->
<!--        FROM-->
<!--            flight_accident_info a-->
<!--                JOIN-->
<!--            MYSQL.HELP_TOPIC b-->
<!--            ON b.help_topic_id &lt; (length(a.choice_segment_ch) - length(replace(a.choice_segment_ch,',',''))+1)-->
<!--        WHERE 1 = 1-->
<!--          AND id = #{id}-->
<!--    </select>-->

<!--    <select id="findChoiceSegment" resultType="java.util.HashMap" databaseId="oracle">-->
<!--        SELECT DISTINCT-->
<!--        SUBSTR(fai.CHOICE_SEGMENT_CH,DECODE(LEVEL,1,1,INSTR(fai.CHOICE_SEGMENT_CH,',',1,LEVEL-1)+1),-->
<!--        DECODE(INSTR(fai.CHOICE_SEGMENT_CH,',',1,LEVEL+1),0,LENGTH(fai.CHOICE_SEGMENT_CH),INSTR(fai.CHOICE_SEGMENT_CH,',',1,LEVEL+1)-1)-INSTR(fai.CHOICE_SEGMENT_CH,',',1,LEVEL)) segmentChKey,-->
<!--        SUBSTR(fai.SEGMENT,DECODE(LEVEL,1,1,INSTR(fai.SEGMENT,',',1,LEVEL-1)+1),-->
<!--        DECODE(INSTR(fai.SEGMENT,',',1,LEVEL+1),0,LENGTH(fai.SEGMENT),INSTR(fai.SEGMENT,',',1,LEVEL+1)-1)-INSTR(fai.SEGMENT,',',1,LEVEL)) segmentChValue-->
<!--        FROM FLIGHT_ACCIDENT_INFO fai WHERE fai.ID = #{id}-->
<!--        CONNECT BY LEVEL &lt;= LENGTH(fai.CHOICE_SEGMENT_CH) - LENGTH(REPLACE(fai.CHOICE_SEGMENT_CH,',',''))+1-->
<!--    </select>-->

    <update id="updAccidentStatusToDo" >
        update flight_accident_info set accident_status = #{accidentStatus}
        where id = #{id}
          and
            get_compensation_count(#{id}) = 0
    </update>

    <update id="updAccidentStatusBatch">
        UPDATE flight_accident_info SET accident_status = #{accidentStatus}
        where 1=1
        <if test="accidentNo != null and accidentNo.size() >0">
            and accident_no in
            <foreach collection="accidentNo" item="items" open="(" separator="," close=")">
                #{items}
            </foreach>
        </if>
    </update>



</mapper>
