package com.swcares.aps.compensation.impl.assist.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.assist.entity.AssistPayRecordDO;
import com.swcares.aps.compensation.model.assist.vo.AssistPayRecordVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: AssistsPayRecordService
 * @projectName aps
 * @description:
 * @date 2022/2/24 13:22
 */
public interface AssistPayRecordService extends IService<AssistPayRecordDO> {
    /**
    * @title isExpire
    * @description 根据手机号修改记录为失效状态
    * @param phone
    * <AUTHOR>
    * @return void
    * @date 2022/2/24 13:30
    */
    void updateExpire(String phone);

    /**
    * @title receivedPaxAmount
    * @description 通过手机号查询半年内协助旅客id
    * @param phone
    * <AUTHOR>
    * @return java.util.Set<java.lang.String>
    * @date 2022/2/24 13:39
    */
    Set<String> receivedPaxAmount(String phone);

    /**
    * @title existPhone
    * @description 查询该手机号是否是第一次使用
    * @param phone
    * <AUTHOR>
    * @return int
    * @date 2022/2/24 13:43
    */
    int existPhone(String phone);

    /**
    * @title findRecord
    * @description 判断手机号是否过期（利用查询是否有超过半年的未失效记录来判断）
    * @param phone
    * <AUTHOR>
    * @return java.util.List<com.swcares.aps.compensation.model.assist.vo.AssistPayRecordVO>
    * @date 2022/2/24 13:47
    */
    List<AssistPayRecordVO> findRecord(String phone);
}