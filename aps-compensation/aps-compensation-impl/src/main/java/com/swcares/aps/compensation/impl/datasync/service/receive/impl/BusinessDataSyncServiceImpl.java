package com.swcares.aps.compensation.impl.datasync.service.receive.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aps.compensation.impl.apply.service.ApplyAuditRecordService;
import com.swcares.aps.compensation.impl.apply.service.ApplyOrderService;
import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.impl.apply.service.PayRecordService;
import com.swcares.aps.compensation.impl.apply.workflow.ApplyWorkflowService;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageTransportAccidentRelMapper;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageTransportInfoMapper;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.baggage.accident.service.impl.BaggageAccidentServiceImpl;
import com.swcares.aps.compensation.impl.compensation.service.AccidentCompensationDomainService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationAuditService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationFactory;
import com.swcares.aps.compensation.impl.compensation.service.impl.CompensationMaterialQueriesServiceImpl;
import com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintAccidentService;
import com.swcares.aps.compensation.impl.complaint.service.impl.PassengerAccidentInfoService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.receive.BusinessDataSyncService;
import com.swcares.aps.compensation.impl.irregularflight.service.*;
import com.swcares.aps.compensation.impl.overbook.service.OverBookAccidentInfoService;
import com.swcares.aps.compensation.model.apply.entity.ApplyAuditDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportAccidentRelDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportInfoDO;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.entity.PassengerAccidentInfoEntity;
import com.swcares.aps.compensation.model.datasync.dto.BusinessDataSyncDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookAccidentInfoDO;
import com.swcares.aps.component.com.decoder.DecoderHandlerException;
import com.swcares.aps.component.com.util.ApsDesensitizedUtil;
import com.swcares.aps.component.com.util.BASE64DecodedMultipartFile;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessDataUploadDTO;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.encrypt.FieldEncryptor;
import com.swcares.components.uc.entity.FileAttachment;
import com.swcares.components.uc.service.FileAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @ClassName：BusinessDataSyncServiceImpl
 * @Description：航司端同步机场端
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/7/16 10:47
 * @version： v1.0
 */
@Slf4j
@Service
public class BusinessDataSyncServiceImpl implements BusinessDataSyncService {
    
    //业务数据处理方法map
    private Map<String, Function<BusinessDataSyncDTO, String>> businessDataHandlerMap;
    private final static String COMPENSATE_TYPE_MATERIAL_DICT_ID ="3"; //实物

    @Autowired
    FlightAccidentInfoService accidentInfoService;
    @Autowired
    OverBookAccidentInfoService overBookAccidentInfoService;
    @Autowired
    ComplaintAccidentService complaintAccidentService;
    @Autowired
    BaggageAccidentService baggageAccidentService;
    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;
    @Autowired
    CompensationPaxInfoService paxInfoService;
    @Autowired
    CompensationFlightInfoService flightInfoService;
    @Autowired
    CompensationRuleRecordService ruleRecordService;
    @Autowired
    PassengerAccidentInfoService passengerAccidentInfoService;
    @Autowired
    private CompensationMaterialQueriesServiceImpl compensationMaterialQueriesService;

    @Autowired
    CompensationAuditService compensationAuditService;

    @Autowired
    private WorkflowApi workflowApi;

    @Autowired
    CompensationFactory compensationFactory;

    @Autowired
    private FileAttachmentService fileAttachmentService;

    @Value("${swcares.minio.bucketName}")
    private String bucketName;

    @Autowired
    ApplyOrderService applyOrderService;
    @Autowired
    ApplyPaxService applyPaxService;
    @Autowired
    ApplyAuditRecordService applyAuditRecordService;
    @Autowired
    PayRecordService payRecordService;
    @Autowired
    private FieldEncryptor fieldEncryptor;
    @Autowired
    private BaggageTransportInfoMapper baggageTransportInfoMapper;
    @Autowired
    private BaggageTransportAccidentRelMapper baggageTransportAccidentRelMapper;


    @PostConstruct
    public void init() {
        businessDataHandlerMap = new HashMap<>();
        //不正常航班-事故单
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_IRREGULAR_FLIGHT+"-"+BusinessDataSyncConstant.DATA_TYPE_ACCIDENT, this::addIrregularFlightAccident);
        //异常行李-事故单
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE+"-"+BusinessDataSyncConstant.DATA_TYPE_ACCIDENT, this::addAbnormalLuggageAccident);
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE+"-"+BusinessDataSyncConstant.DATA_TYPE_TRANSPORT, this::addTransportationAndRef);
        //航班超售-事故单
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_OVER_BOOKING+"-"+BusinessDataSyncConstant.DATA_TYPE_ACCIDENT, this::addOverBookingAccident);
        //旅客投诉-事故单
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_COMPLAINT+"-"+BusinessDataSyncConstant.DATA_TYPE_ACCIDENT, this::addComplaintAccident);

        //补偿单-不正常航班、航班超售、旅客投诉
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_IRREGULAR_FLIGHT+"-"+BusinessDataSyncConstant.DATA_TYPE_COMPENSATION, this::saveCompensationRelevant);
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_OVER_BOOKING+"-"+BusinessDataSyncConstant.DATA_TYPE_COMPENSATION, this::saveCompensationRelevant);
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_COMPLAINT+"-"+BusinessDataSyncConstant.DATA_TYPE_COMPENSATION, this::saveCompensationRelevant);
        //补偿单-异常行李
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE+"-"+BusinessDataSyncConstant.DATA_TYPE_COMPENSATION, this::addAbnormalLuggageCompensation);

        //申领单
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_IRREGULAR_FLIGHT+"-"+BusinessDataSyncConstant.DATA_TYPE_APPLY, this::addApplyOrder);
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE+"-"+BusinessDataSyncConstant.DATA_TYPE_APPLY, this::addApplyOrder);
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_OVER_BOOKING+"-"+BusinessDataSyncConstant.DATA_TYPE_APPLY, this::addApplyOrder);
        businessDataHandlerMap.put(BusinessDataSyncConstant.BUSINESS_COMPLAINT+"-"+BusinessDataSyncConstant.DATA_TYPE_APPLY, this::addApplyOrder);
        // 添加更多的映射

    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void compensationBusinessDataSync(BusinessDataUploadDTO businessDataDTO) {
        log.info("【航司端接收机场端的业务数据】收到请求: {}", JSONUtil.toJsonStr(businessDataDTO));
        //  航司租户id获取
        String receiverCustomer = businessDataDTO.getReceiverCustomer();
        Long tenantId = compensationOrderInfoService.getTenantIdByCode(receiverCustomer);
        if(TenantHolder.getTenant() == null || TenantHolder.getTenant() == GlobalConstants.TENANT_DEFAULT){
            TenantHolder.setTenant(tenantId);
        }

        String data = businessDataDTO.getData();
        BusinessDataSyncDTO dataSyncDTO = JSON.parseObject(data, BusinessDataSyncDTO.class);
        dataSyncDTO.setReceiverCustomerId(tenantId);
        String key = dataSyncDTO.getBusinessType() + "-" + dataSyncDTO.getDataType();
        Function<BusinessDataSyncDTO, String> function = businessDataHandlerMap.get(key);
        if (function != null) {
             function.apply(dataSyncDTO); // 这里的"input"是传递给方法的参数
        } else {
            throw new IllegalArgumentException("No method found for business type: " + dataSyncDTO.getBusinessType() + " and data type: " + dataSyncDTO.getDataType());
        }
    }


    public String addTransportationAndRef(BusinessDataSyncDTO businessDataSyncDTO) {
        BaggageTransportInfoDO baggageTransportInfoDO = businessDataSyncDTO.getBaggageTransportInfoDO();
        List<BaggageTransportAccidentRelDO> baggageTransportAccidentRelDOS = businessDataSyncDTO.getBaggageTransportAccidentRelDOS();

        if (null == baggageTransportInfoDO) {
            log.error("--【航司端接收机场端的业务数据-运输单】处理运输单类型:异常行李 ，接收到的运输单数据为空！");
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }

        // 设置租户ID
        baggageTransportInfoDO.setTenantId(businessDataSyncDTO.getReceiverCustomerId());

        // 保存运输单
        try {
            baggageTransportInfoMapper.insert(baggageTransportInfoDO);
        } catch (Exception e) {
            log.error("--【航司端接收机场端的业务数据-运输单】保存运输单异常，运输单号：{}", baggageTransportInfoDO.getTransportNumber(), e);
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }

        log.info("--【航司端接收机场端的业务数据-运输单】处理运输单类型:异常行李 ,保存运输单:[{}]", JSON.toJSONString(baggageTransportInfoDO));

        // 保存运输单关联表数据
        if (CollectionUtils.isNotEmpty(baggageTransportAccidentRelDOS)) {
            baggageTransportAccidentRelDOS.stream().forEach(rel -> {
                rel.setTenantId(businessDataSyncDTO.getReceiverCustomerId());
            });

            for (BaggageTransportAccidentRelDO relDO : baggageTransportAccidentRelDOS) {
                try {
                    baggageTransportAccidentRelMapper.insert(relDO);
                    log.info("--【航司端接收机场端的业务数据-运输单关联】保存运输单关联成功，运输单ID：{}，事故单号：{}", relDO.getTransportId(), relDO.getAccidentNo());
                } catch (Exception e) {
                    log.error("--【航司端接收机场端的业务数据-运输单关联】保存运输单关联异常，运输单ID：{}，事故单号：{}", relDO.getTransportId(), relDO.getAccidentNo(), e);
                    throw new BusinessException(CommonErrors.CREATE_ERROR);
                }
            }
        }
        return baggageTransportInfoDO.getId().toString();
    }


    public String addIrregularFlightAccident(BusinessDataSyncDTO dataSyncDTO){
        FlightAccidentInfoDO accidentInfoDO = dataSyncDTO.getFlightAccidentInfoDO();
        if(null == accidentInfoDO){
            log.error("--【航司端接收机场端的业务数据-事故单】处理事故单类型:不正常航班 ，接收到的业务数据为空！");
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        accidentInfoDO.setTenantId(dataSyncDTO.getReceiverCustomerId());
        boolean saveFlag = accidentInfoService.saveOrUpdate(accidentInfoDO);
        log.info("--【航司端接收机场端的业务数据-事故单】处理事故单类型:不正常航班 ，处理结果：{},保存事故单:[{}]",saveFlag, JSON.toJSONString(accidentInfoDO));
        return accidentInfoDO.getId().toString();
    }

    public String addAbnormalLuggageAccident(BusinessDataSyncDTO dataSyncDTO){
        BaggageAccidentInfoDO accidentInfoDO = dataSyncDTO.getBaggageAccidentInfoDO();
        BusinessDataSyncDTO.BusinessImgDO businessImgDO = dataSyncDTO.getBusinessImgDO();
        if(null == accidentInfoDO){
            log.error("--【航司端接收机场端的业务数据-事故单】处理事故单类型:异常行李 ，接收到的业务数据为空！");
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }

        accidentInfoDO.setTenantId(dataSyncDTO.getReceiverCustomerId());
        //处理图片上传
        if(ObjectUtils.isNotEmpty(businessImgDO)){
            try {
                if(CollectionUtils.isNotEmpty(businessImgDO.getPaxImgs())){
                    String paxImg = "";
                    List<MultipartFile> result = new ArrayList<>();
                    businessImgDO.getPaxImgs().forEach(e->{
                        MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                        result.add(f);
                    });
                    List<FileAttachment> paxFiles = null;

                    paxFiles = fileAttachmentService.uploadFile(result,bucketName,null);

                    for (int i = 0; i < paxFiles.size(); i++) {
                        if(i==0){
                            paxImg = paxImg+paxFiles.get(i).getId();
                        }else {
                            paxImg = paxImg+","+paxFiles.get(i).getId();
                        }
                    }
                    accidentInfoDO.setCollectIdentityPaxPhotos(paxImg);
                }
                if(CollectionUtils.isNotEmpty(businessImgDO.getVoucherImgs())){
                    String voucherImg = "";
                    List<MultipartFile> result = new ArrayList<>();
                    businessImgDO.getVoucherImgs().forEach(e->{
                        MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                        result.add(f);
                    });
                    List<FileAttachment> voucherFiles = fileAttachmentService.uploadFile(result,bucketName,null);
                    for (int i = 0; i < voucherFiles.size(); i++) {
                        if(i==0){
                            voucherImg = voucherImg+voucherFiles.get(i).getId();
                        }else {
                            voucherImg = voucherImg+","+voucherFiles.get(i).getId();
                        }
                    }
                    accidentInfoDO.setCollectIdentityVoucherPhotos(voucherImg);
                }
                if(CollectionUtils.isNotEmpty(businessImgDO.getBaggageImgs())){
                    String baggageImg = "";
                    List<MultipartFile> result = new ArrayList<>();
                    businessImgDO.getBaggageImgs().forEach(e->{
                        MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                        result.add(f);
                    });
                    List<FileAttachment> baggageFiles = fileAttachmentService.uploadFile(result,bucketName,null);
                    for (int i = 0; i < baggageFiles.size(); i++) {
                        if(i==0){
                            baggageImg = baggageImg+baggageFiles.get(i).getId();
                        }else {
                            baggageImg = baggageImg+","+baggageFiles.get(i).getId();
                        }
                    }
                    accidentInfoDO.setCollectIdentityBaggagePhotos(baggageImg);
                }
            } catch (Exception e) {
                log.error("--【航司端接收机场端的业务数据-事故单】处理事故单类型:异常行李 ,事故单号：{}，图片上传异常!!",accidentInfoDO.getAccidentNo(), e);
                throw new BusinessException(CommonErrors.CREATE_ERROR);
            }
        }

        boolean saveFlag = baggageAccidentService.saveOrUpdate(accidentInfoDO);
        log.info("--【航司端接收机场端的业务数据-事故单】处理事故单类型:异常行李 ，处理结果：{},保存事故单:[{}]",saveFlag, JSON.toJSONString(accidentInfoDO));
        return accidentInfoDO.getId().toString();
    }

    public String addOverBookingAccident(BusinessDataSyncDTO dataSyncDTO){
        OverBookAccidentInfoDO accidentInfoDO = dataSyncDTO.getOverBookAccidentInfoDO();
        if(null == accidentInfoDO){
            log.error("--【航司端接收机场端的业务数据-事故单】处理事故单类型:航班超售 ，接收到的业务数据为空！");
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        accidentInfoDO.setTenantId(dataSyncDTO.getReceiverCustomerId());
        //上传图片
        BusinessDataSyncDTO.BusinessImgDO businessImgDO = dataSyncDTO.getBusinessImgDO();
        try{
            if(ObjectUtils.isNotEmpty(businessImgDO) && CollectionUtils.isNotEmpty(businessImgDO.getFileUrl())){
                String imgUrl = "";
                List<MultipartFile> result = new ArrayList<>();
                businessImgDO.getFileUrl().forEach(e->{
                    MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                    result.add(f);
                });
                List<FileAttachment> imgFiles = fileAttachmentService.uploadFile(result,bucketName,null);
                for (int i = 0; i < imgFiles.size(); i++) {
                    if(i==0){
                        imgUrl = imgUrl+imgFiles.get(i).getId();
                    }else {
                        imgUrl = imgUrl+","+imgFiles.get(i).getId();
                    }
                }
                accidentInfoDO.setImgUrl(imgUrl);
            }
        } catch (Exception e) {
            log.error("--【航司端接收机场端的业务数据-事故单】处理事故单类型:航班超售 ,事故单号：{}，图片上传异常!!",accidentInfoDO.getAccidentNo(), e);
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }

        boolean saveFlag = overBookAccidentInfoService.saveOrUpdate(accidentInfoDO);
        log.info("--【航司端接收机场端的业务数据-事故单】处理事故单类型:航班超售 ，处理结果：{},保存事故单:[{}]",saveFlag, JSON.toJSONString(accidentInfoDO));
        return accidentInfoDO.getId().toString();
    }

    public String addComplaintAccident(BusinessDataSyncDTO dataSyncDTO){
        ComplaintAccidentInfoEntity accidentInfoEntity = dataSyncDTO.getComplaintAccidentInfoEntity();
        List<PassengerAccidentInfoEntity> passengerAccidentInfoEntityList = dataSyncDTO.getPassengerAccidentInfoEntity();
        if(null == accidentInfoEntity){
            log.error("--【航司端接收机场端的业务数据-事故单】处理事故单类型:旅客投诉 ，接收到的业务数据为空！");
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        if (CollectionUtils.isNotEmpty(passengerAccidentInfoEntityList)){
            passengerAccidentInfoEntityList.stream().forEach(e->e.setTenantId(dataSyncDTO.getReceiverCustomerId()));
            for(PassengerAccidentInfoEntity entity:passengerAccidentInfoEntityList){
                passengerAccidentInfoService.saveOrUpdate(entity);
            }
        }
        accidentInfoEntity.setTenantId(dataSyncDTO.getReceiverCustomerId());
        //上传图片
        BusinessDataSyncDTO.BusinessImgDO businessImgDO = dataSyncDTO.getBusinessImgDO();
        try{
            if(ObjectUtils.isNotEmpty(businessImgDO) && CollectionUtils.isNotEmpty(businessImgDO.getFileUrl())){
                String imgUrl = "";
                List<MultipartFile> result = new ArrayList<>();
                businessImgDO.getFileUrl().forEach(e->{
                    MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                    result.add(f);
                });
                List<FileAttachment> imgFiles = fileAttachmentService.uploadFile(result,bucketName,null);
                for (int i = 0; i < imgFiles.size(); i++) {
                    if(i==0){
                        imgUrl = imgUrl+imgFiles.get(i).getId();
                    }else {
                        imgUrl = imgUrl+","+imgFiles.get(i).getId();
                    }
                }
                accidentInfoEntity.setFilesUrl(imgUrl);
            }
        } catch (Exception e) {
            log.error("--【航司端接收机场端的业务数据-事故单】处理事故单类型:旅客投诉 ,事故单号：{}，图片上传异常!!",accidentInfoEntity.getAccidentId(), e);
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        boolean saveFlag = complaintAccidentService.saveOrUpdate(accidentInfoEntity);
        log.info("--【航司端接收机场端的业务数据-事故单】处理事故单类型:旅客投诉 ，处理结果：{},保存事故单:[{}]",saveFlag, JSON.toJSONString(accidentInfoEntity));
        return accidentInfoEntity.getId().toString();
    }






    /***
     * @title saveCompensationRelevant
     * @description 保存赔偿单相关的类
     * <AUTHOR>
     * @date 2024/7/11 16:04
     * @return void
     */
    private String saveCompensationRelevant(BusinessDataSyncDTO dataSyncDTO){
        //校验参数是否为空
        this.validationCompensation(dataSyncDTO);
        CompensationOrderInfoDO orderInfoDO = dataSyncDTO.getCompensationOrderInfoDO();
        CompensationFlightInfoDO flightInfoDO = dataSyncDTO.getCompensationFlightInfoDO();
        List<CompensationPaxInfoDO> paxInfoDO = dataSyncDTO.getCompensationPaxInfoDO();
        List<CompensationRuleRecordDO> ruleRecordDO = dataSyncDTO.getCompensationRuleRecordDO();

        orderInfoDO.setTenantId(dataSyncDTO.getReceiverCustomerId());

        paxInfoDO.stream().forEach(d->{
            d.setTenantId(dataSyncDTO.getReceiverCustomerId());
            log.info("--【航司端接收机场端的业务数据-补偿单】补偿单号：{}， 旅客姓名：{}，证件号：{}",orderInfoDO.getId(), d.getPaxName(), ApsDesensitizedUtil.idCardNum(d.getIdNo()));
        });

        CompensationOrderInfoDO oldOrderInfoDO = compensationOrderInfoService.getById(orderInfoDO.getId());

        compensationOrderInfoService.saveOrUpdate(orderInfoDO);
        Map<String,Object> columnMap = new HashMap<>();
        columnMap.put("order_id",orderInfoDO.getId());
        paxInfoService.removeByMap(columnMap);
        paxInfoDO.stream().forEach(d->{
            paxInfoService.saveOrUpdate(d);
        });

        if(flightInfoDO != null){
            flightInfoDO.setTenantId(dataSyncDTO.getReceiverCustomerId());
            flightInfoService.removeByMap(columnMap);
            flightInfoService.saveOrUpdate(flightInfoDO);
        }

        if(ruleRecordDO != null && ruleRecordDO.size()>0){
            ruleRecordService.removeByMap(columnMap);
            ruleRecordDO.stream().forEach(d->d.setTenantId(dataSyncDTO.getReceiverCustomerId()));
            ruleRecordService.saveOrUpdateBatch(ruleRecordDO);
        }
        //保存事故单信息
        if(BusinessDataSyncConstant.BUSINESS_IRREGULAR_FLIGHT.equals(dataSyncDTO.getBusinessType())){
            this.addIrregularFlightAccident(dataSyncDTO);
        }else if(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE.equals(dataSyncDTO.getBusinessType())){
            this.addAbnormalLuggageAccident(dataSyncDTO);
        }else if(BusinessDataSyncConstant.BUSINESS_OVER_BOOKING.equals(dataSyncDTO.getBusinessType())){
            this.addOverBookingAccident(dataSyncDTO);
        }else if(BusinessDataSyncConstant.BUSINESS_COMPLAINT.equals(dataSyncDTO.getBusinessType())){
            this.addComplaintAccident(dataSyncDTO);
        }


        AccidentCompensationDTO compensationDTO = compensationFactory.createByOrderId(String.valueOf(orderInfoDO.getId()));
        compensationDTO.setStartSyncWorkflowInfoDTO(dataSyncDTO.getStartSyncWorkflowInfoDTO());
        try {
            CurrentTaskActivityVO activityVO = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(String.valueOf(compensationDTO.getId())).build()).getData();
            CurrentTaskActivityDTO currentTaskActivity = activityVO.getCurrentTaskActivityDTOS().get(0);
            if(WorkflowUtils.isSubmitterTask(currentTaskActivity.getNodeKey())){
                compensationAuditService.submitterAuditProcess(compensationDTO);
            }
        }catch (DecoderHandlerException e1){
            if(e1.getCode()==AccidentCompensationDomainService.PROCESS_INSTANCE_NOT_EXIST){
                compensationAuditService.startAuditProcess(compensationDTO);
            }else{
                log.error("--【航司端接收机场端的业务数据-补偿单审核异常】保存补偿单相关信息,流程实例不存在，不进行处理！{}",e1);
                throw e1;
            }
        }


        return "";

    }

    private void validationCompensation(BusinessDataSyncDTO dataSyncDTO){
        if(null == dataSyncDTO.getCompensationOrderInfoDO() || CollectionUtils.isEmpty(dataSyncDTO.getCompensationPaxInfoDO())){
            log.error("--【航司端接收机场端的业务数据-赔偿单】保存赔偿单相关信息, orderInfoDO、flightInfoDO、paxInfoDO存在空对象，传入参数不完整，不进行处理！");
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
    }

    private void validationApply(BusinessDataSyncDTO dataSyncDTO){
        if(null == dataSyncDTO.getApplyOrderDO()
                || CollectionUtils.isEmpty(dataSyncDTO.getApplyPaxDOList())){
            log.error("--【航司端接收机场端的业务数据-申领单】保存申领单相关信息, applyOrderDO、applyPaxDOList存在空对象，传入参数不完整，不进行处理！");
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
    }


    /***
     * @title addAbnormalLuggageCompensation
     * @description 保存异常行李-赔偿单
     * <AUTHOR>
     * @date 2024/7/12 10:09
     * @param dataSyncDTO
     * @return void
     */
    private String addAbnormalLuggageCompensation(BusinessDataSyncDTO dataSyncDTO){
        //校验参数是否为空
        this.validationCompensation(dataSyncDTO);
        CompensationOrderInfoDO orderInfoDO = dataSyncDTO.getCompensationOrderInfoDO();
//        dataSyncDTO.getCompensationExpressInfoDO();//补偿快递
        List<CompensationMaterialInfoDO> materialInfoDO = dataSyncDTO.getCompensationMaterialInfoDO();//实物
        if(COMPENSATE_TYPE_MATERIAL_DICT_ID.equals(orderInfoDO.getCompensateType())
                && CollectionUtils.isEmpty(materialInfoDO)){
            //箱包补偿单，箱包信息必填
            log.error("--【航司端接收机场端的业务数据-赔偿单】异常行李-赔偿单为箱包补偿,箱包信息为空，不进行处理！");
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        this.saveCompensationRelevant(dataSyncDTO);
        compensationMaterialQueriesService.saveOrUpdateBatch(materialInfoDO);
        return "";
    }

    @Autowired
    ApplyWorkflowService applyWorkflowService;
    private String addApplyOrder(BusinessDataSyncDTO dataSyncDTO) {
        //校验参数是否为空
        this.validationApply(dataSyncDTO);

        ApplyOrderDO applyOrderDO = dataSyncDTO.getApplyOrderDO();
        List<ApplyAuditDO> applyAuditDOS = dataSyncDTO.getApplyAuditDOS();
        List<ApplyPaxDO> applyPaxDOList = dataSyncDTO.getApplyPaxDOList();
        List<PayRecordDO> payRecordDOS = dataSyncDTO.getPayRecordDOS();
        List<CompensationPaxInfoDO> compensationPaxInfoDO = dataSyncDTO.getCompensationPaxInfoDO();
        log.info("--【航司端接收机场端的业务数据-申领单】申领单号：{}， 申领旅客姓名：{}，证件号：{}",applyOrderDO.getApplyUser(), applyOrderDO.getIdNo());

        try {
            //处理图片
            //本人和代领都存在签名图片（申领时必填）
            if(StringUtils.isNotEmpty(applyOrderDO.getSignFile())){
                FileAttachment fileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(applyOrderDO.getSignFile()),bucketName,null,null);
                applyOrderDO.setSignFile(fileAttachment.getId().toString());
            }

            if(StringUtils.isNotEmpty(applyOrderDO.getCollectIdentityCardPhoto())){
                FileAttachment fileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(applyOrderDO.getCollectIdentityCardPhoto()),bucketName,null,null);
                applyOrderDO.setCollectIdentityCardPhoto(fileAttachment.getId().toString());
            }

            //代领人的图片
            for(ApplyPaxDO applyPaxDO : applyPaxDOList){
                applyPaxDO.setTenantId(dataSyncDTO.getReceiverCustomerId());
                if(StringUtils.isNotEmpty(applyPaxDO.getPaxIdentityCardPhoto())){
                    FileAttachment fileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(applyPaxDO.getPaxIdentityCardPhoto()),bucketName,null,null);
                    applyPaxDO.setPaxIdentityCardPhoto(fileAttachment.getId().toString());
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
            log.error("--【航司端接收机场端的业务数据-申领单】处理,申领单id：{},申领单号：{}，图片上传异常!!",applyOrderDO.getId(),applyOrderDO.getApplyCode(), e);
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }

        applyOrderDO.setTenantId(dataSyncDTO.getReceiverCustomerId());
        applyOrderService.saveOrUpdate(applyOrderDO);
        applyPaxService.saveOrUpdateBatch(applyPaxDOList);
        if(CollectionUtils.isNotEmpty(applyAuditDOS)){
            applyAuditDOS.stream().forEach(d->{
                d.setTenantId(dataSyncDTO.getReceiverCustomerId());
                applyAuditRecordService.saveOrUpdate(d);
            });
        }
        if(CollectionUtils.isNotEmpty(payRecordDOS)){
            payRecordDOS.stream().forEach(d->{
                d.setTenantId(dataSyncDTO.getReceiverCustomerId());
                payRecordService.saveOrUpdate(d);
            });
        }

        compensationPaxInfoDO.stream().forEach(d->{
            d.setTenantId(dataSyncDTO.getReceiverCustomerId());
            paxInfoService.saveOrUpdate(d);
        });
  /*      if("1".equals(applyOrderDO.getApplyWay())){
            //发起流程
            try {
                applyWorkflowService.startWorkflow(applyOrderDO.getId().toString(),"-1");
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException(CompensationException.AUDIT_ERROR,"发起审核流程失败");
            }
        }*/
        return "";
    }

}
