package com.swcares.aps.compensation.impl.irregularflight.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.swcares.aps.compensation.impl.apply.service.PayRecordService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationFactory;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderStatusService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationConstant;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateDroolsRuleCodeEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.RowPermissionTableName;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationOrderInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.*;
import com.swcares.aps.compensation.impl.irregularflight.workflow.IrregularFlightWorkflowService;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeService;
import com.swcares.aps.compensation.impl.util.CompensationOrderNoUtils;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.*;
import com.swcares.aps.compensation.model.irregularflight.entity.*;
import com.swcares.aps.compensation.model.irregularflight.vo.*;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeItemDTO;
import com.swcares.aps.component.com.decoder.DecoderHandlerException;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.aps.component.drools.impl.service.DroolsRuleService;
import com.swcares.aps.component.permission.util.RowPermissionUtil;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.baseframe.utils.lang.StringUtils;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.kie.api.runtime.KieSession;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.service.impl.CompensationOrderInfoServiceImpl <br>
 * Description：赔偿单信息 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class CompensationOrderInfoServiceImpl extends ServiceImpl<CompensationOrderInfoMapper, CompensationOrderInfoDO> implements CompensationOrderInfoService {
    private static final int PROCESS_INSTANCE_NOT_EXIST = 31002;
    //事故单
    @Autowired
    FlightAccidentInfoService accidentInfoService;
    //旅客信息
    @Autowired
    CompensationPaxInfoService paxInfoService;
    //航班信息
    @Autowired
    CompensationFlightInfoService flightInfoService;
    //规则记录
    @Autowired
    CompensationRuleRecordService ruleRecordService;
    //规则引擎
    @Autowired
    DroolsRuleService droolsRuleService;
    @Autowired
    WorkflowApi workflowApi;

    @Autowired
    CompensationAuditInfoService compensationAuditInfoService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IrregularFlightWorkflowService irregularFlightWorkflowService;

    @Autowired
    private CompensationOrderStatusService compensationOrderStatusService;

    @Autowired
    private CompensationFactory compensationFactory;

    @Autowired
    private CompensationOrderInfoMapper compensationOrderInfoMapper;

    @Autowired
    private Redisson redisson;

    @Autowired
    PayRecordService payRecordService;

    @Autowired
    BusinessPrivilegeService businessPrivilegeService;

    //补偿单编号标识
    private static final String ORDER_NO_PREFIX = "IFC";

    // 经济舱标识
    private static final String E_CLASS_TYPE = "1";

    // 公务舱标识
    private static final String B_CLASS_TYPE = "2";

    // 领取状态为未领取
    private static final String RECEIVE_STATUS_UNCLAIMED = "0";

    public boolean logicRemoveById(Long id) {
        CompensationOrderInfoDO compensationOrderInfoDO = getBaseMapper().selectById(id);
        CompensationOrderInfoDO entity = new CompensationOrderInfoDO();
        entity.setId(id);
        entity.setStatus(CompensateStatusEnum.DRAFT.getKey());//草稿
        LambdaQueryWrapper<CompensationOrderInfoDO> wrapper = Wrappers.lambdaQuery(entity);
        boolean retBool = SqlHelper.retBool(getBaseMapper().delete(wrapper));
        if(retBool){
            Map<String,Object> columnMap = new HashMap<>();
            columnMap.put("order_id",id);
            //删除旅客
            paxInfoService.removeByMap(columnMap);
            //删除赔偿标准
            ruleRecordService.removeByMap(columnMap);
            //删除航班
            flightInfoService.removeByMap(columnMap);
        }
        return retBool;
    }

    public IPage<CompensationOrderInfoExamineVO> pages(CompensationOrderInfoPagedDTO dto) {
        log.info("【aps-compensation-impl】不正常航班赔偿单列表查询--查询参数【{}】",JSONUtil.toJsonStr(dto));
        if(StrUtil.isBlank(dto.getFlightStartDate()) || StrUtil.isBlank(dto.getFlightEndDate())){
            throw new BusinessException(CommonErrors.UNKNOW_ERROR);
        }
        //处理支持多选的字段
        if(ObjectUtils.isNotEmpty(dto.getFcType())){
            List<String> fcType = new ArrayList<>(Arrays.asList(dto.getFcType().split(",")));
            dto.setFcTypeList(fcType);
        }
        if(ObjectUtils.isNotEmpty(dto.getServiceCity())){
            List<String> serviceCity = new ArrayList<>(Arrays.asList(dto.getServiceCity().split(",")));
            dto.setServiceCityList(serviceCity);
        }
        //将场站范围，事故类型传入
        log.info("【aps-compensation-impl】不正常航班赔偿单列表查询<--------行数据权限处理开始-------->");
        String tableName= RowPermissionTableName.COMPENSATION;
        List<String> idTypes = new ArrayList<>();
        idTypes.add(RowPermissionTableName.ACCIDENTTYPES);
        idTypes.add(RowPermissionTableName.WORKSTATIONS);
        Map<String,List<String>> mapTypes = RowPermissionUtil.getRowValues(tableName,idTypes);
        if(ObjectUtils.isNotEmpty(mapTypes)){
            List<String> accidentTypes = mapTypes.get(RowPermissionTableName.ACCIDENTTYPES);
            List<String> workStations = mapTypes.get(RowPermissionTableName.WORKSTATIONS);
            if(ObjectUtils.isNotEmpty(accidentTypes)){
                dto.setAccidentTypes(accidentTypes);
            }
            if(ObjectUtils.isNotEmpty(workStations)){
                dto.setWorkStations(workStations);
            }
        }
        log.info("【aps-compensation-impl】不正常航班赔偿单列表查询<--------行数据权限处理结束-------->");
        return baseMapper.pages(dto, dto.createPage());
    }

    @Override
    public Map<String,Object> saveOrderInfoAndAudit(CompensationSyntheticalSaveDTO dto) throws Exception {
        long startTime = System.currentTimeMillis();
        log.info("【aps-compensation-impl】耗时统计——开始保存赔偿单信息-------");
        log.info("【aps-compensation-impl】编辑保存赔偿单信息，请求参数[{}]", JSONUtil.toJsonStr(dto));
        //先查询订单原状态，区分是否是驳回保存提交
        CompensationOrderInfoDO orderInfoDO = null;
        if(ObjectUtils.isNotEmpty(dto.getOrderInfoDTO().getId())) {
            orderInfoDO = this.getBaseMapper().selectById(dto.getOrderInfoDTO().getId());
        }
        RLock lock = redisson.getLock("CompensationSave");
        // 尝试加锁 返回boolean类型
        boolean resLock = false;
        try {
            resLock = lock.tryLock(CompensationConstant.SAVE_REDIS_LOCK_TIME, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        try {
            if (resLock) {
                //保存赔偿单
                this.saveOrderInfo(dto);
            }else{
                log.error("【aps-compensation-impl】-不正常航班工作流-start发起审核流程操作，获取分布式锁失败：{}",JSONUtil.toJsonStr(dto));
                throw new BusinessException(CompensationException.AUDIT_ERROR,"不正常航班-获取分布式锁失败");
            }
        }finally {
            lock.unlock();
        }
        log.info("【aps-compensation-impl】耗时统计——保存赔偿单基础信息表及规则引擎计算，~累计~耗时【{}】-------",  (System.currentTimeMillis() - startTime));

        CompensationOrderInfoDO orderInfoDO1 = this.getBaseMapper()
                .selectOne(new QueryWrapper<CompensationOrderInfoDO>()
                        .lambda()
                        .eq(CompensationOrderInfoDO::getOrderNo,dto.getOrderInfoDTO()
                                .getOrderNo()));
        dto.getOrderInfoDTO().setId(orderInfoDO1.getId());
        log.info("【aps-compensation-impl】耗时统计——根据赔偿单号获取赔偿单id，目的是为了下面询当前用户是否存在流程（针对赔偿单驳回重提提交）时用到参数orderId（主键），~累计~耗时【{}】-------",  (System.currentTimeMillis() - startTime));

        //驳回后再次发起审核流程
        //判断是否存在待处理任务，为了解决驳回后的单子先保存为草稿再提交，避免重复发起流程
        CurrentTaskActivityVO currentTaskActivityVOBaseResult = null;
        try {
            currentTaskActivityVOBaseResult = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(String.valueOf(orderInfoDO1.getId())).build()).getData();
            log.info("【aps-compensation-impl】赔偿单id:【{}】,流程待处理结果：【{}】", orderInfoDO1.getId(), JSONUtil.toJsonStr(currentTaskActivityVOBaseResult));
        }catch (DecoderHandlerException b){
            if(b.getCode()==PROCESS_INSTANCE_NOT_EXIST){
                log.info("【aps-compensation-impl】流程实例不存在，实例id:【{}】",orderInfoDO1.getId());
            }else{
                throw b;
            }
        }

        log.info("【aps-compensation-impl】耗时统计——查询当前用户是否存在流程（针对赔偿单驳回重提提交），~累计~耗时：【{}】-------",  (System.currentTimeMillis() - startTime));

        if(dto.getOrderInfoDTO().getCurrentAmount()==null
                && CollectionUtils.isNotEmpty(dto.getRuleRecordDTO())
                && dto.getRuleRecordDTO().get(0).getCpsNum()!=null){
            dto.getOrderInfoDTO().setCurrentAmount(dto.getRuleRecordDTO().get(0).getCpsNum());
        }
        if(ObjectUtils.isNotEmpty(orderInfoDO) && CompensateStatusEnum.AUDIT_PROCESS.getKey().equals(dto.getOrderInfoDTO().getStatus())
                && ObjectUtils.isNotEmpty(currentTaskActivityVOBaseResult)){
            log.info("【aps-compensation-impl】编辑保存赔偿单信息-驳回后再次发起审核流程。赔偿单号[{}]", dto.getOrderInfoDTO().getOrderNo());
            Map<String,Object> map = irregularFlightWorkflowService.submitterAuditProcess(dto);

            log.info("【aps-compensation-impl】耗时统计——驳回的赔偿单再次提交，调用工作流引擎返回后拿到结果，~累计~耗时：【{}】-------",  (System.currentTimeMillis() - startTime));
            return map;
        }

        //发起审核流程，orderId为null，需要查
        if(CompensateStatusEnum.AUDIT_PROCESS.getKey().equals(dto.getOrderInfoDTO().getStatus())){
            log.info("【aps-compensation-impl】编辑保存赔偿单信息-赔偿单保存成功，赔偿单状态为【审核中】，发起审核流程。赔偿单号[{}]", dto.getOrderInfoDTO().getOrderNo());
            Map<String,Object> map = irregularFlightWorkflowService.startAuditProcess(dto);
            log.info("【aps-compensation-impl】耗时统计——赔偿单基本信息已经入库后，调用工作流引擎启动流程返回后拿到结果，~累计~耗时：【{}】-------",  (System.currentTimeMillis() - startTime));
            return map;
        }

        return new HashMap<>();
    }

    @Override
    public boolean saveOrderInfo(CompensationSyntheticalSaveDTO dto) {
        // 获取当前用户id
        String userId = ApsUserUtils.getCreatedBy();
        boolean bool = false;
        CompensationOrderInfoDO orderInfoDO = ObjectUtils.copyBean(dto.getOrderInfoDTO(), CompensationOrderInfoDO.class);
        List<CompensationPaxInfoDTO> listPaxDTO = dto.getPaxInfoDTO();
        CompensationFlightInfoDO flightInfoDO = ObjectUtils.copyBean(dto.getFlightInfoDTO(), CompensationFlightInfoDO.class);
        List<CompensationRuleRecordDTO> listRuleRecordDTO = dto.getRuleRecordDTO();
        //新建-更新事故单状态
        this.updAccidentStatus(dto);
        // add belongAirline and source
        FlightAccidentInfoDO accidentDo = accidentInfoService.getById(dto.getAccidentInfoDTO().getId());
        orderInfoDO.setBelongAirline(accidentDo.getBelongAirline());
        orderInfoDO.setSource(accidentDo.getAccidentSource());
        dto.getOrderInfoDTO().setSource(accidentDo.getAccidentSource());
        orderInfoDO.setBelongAirlineAbbr(accidentDo.getBelongAirlineAbbr());
        //生成赔偿单号
        String belongAirline = dto.getAccidentInfoDTO().getFlightNo().substring(0, 2);
        String orderNo = createCompensationOrderNo(dto.getAccidentInfoDTO().getAccidentType(),
                belongAirline);
        //编辑已有赔偿单，先删再存
        if (StringUtils.isNotEmpty(orderInfoDO.getOrderNo())) {
            orderNo = orderInfoDO.getOrderNo();
            //编辑【赔偿单草稿||驳回状态】删除关联的信息
            this.deleteOrderInfo(orderInfoDO.getId());
        }
        //此处设置（新建无赔偿单号）orderNo为了记录日志。
        dto.getOrderInfoDTO().setOrderNo(orderNo);
        //计算赔偿金额
        BigDecimal totalMoney = calculateCompensationSum(listRuleRecordDTO, listPaxDTO);
        //保存赔偿单
        orderInfoDO.setOrderNo(orderNo);
        orderInfoDO.setAccidentId(dto.getAccidentInfoDTO().getId());
        orderInfoDO.setAccidentNo(dto.getAccidentInfoDTO().getAccidentNo());
        orderInfoDO.setSumMoney(totalMoney);
        orderInfoDO.setCreatedBy(userId);
        orderInfoDO.setCreatedTime(LocalDateTime.now());
        orderInfoDO.setUpdatedBy(userId);
        //此处设置（新建无赔偿单号）totalMoney。
        dto.getOrderInfoDTO().setSumMoney(totalMoney);
        bool = this.save(orderInfoDO);
        if (!bool) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        //保存航班信息flightInfoService
        flightInfoDO.setOrderId(orderInfoDO.getId());
        flightInfoDO.setCreatedBy(userId);
        flightInfoDO.setCreatedTime(LocalDateTime.now());
        bool = flightInfoService.save(flightInfoDO);
        if (!bool) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        //保存旅客paxInfoService
        bool = savePax(listPaxDTO, orderInfoDO.getId());
        if (!bool) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        //保存赔偿规则ruleRecordService
        bool = saveRuleRecord(listRuleRecordDTO, orderInfoDO.getId(), dto.getAccidentInfoDTO().getId());

        //获取成人的信息，目的是审核流获取单个成人的金额进行网关判断
        List<CompensationPaxInfoDTO> paxInfoDTO = dto.getPaxInfoDTO();
        //取任意一个成人的赔偿金额进行流程判断
        for(CompensationPaxInfoDTO paxInfoDTO1 :paxInfoDTO){
            if("0".equals(paxInfoDTO1.getIsChild())){
                dto.getOrderInfoDTO().setCurrentAmount(paxInfoDTO1.getCurrentAmount());
                break;
            }
        }

        log.info("【aps-compensation-impl】提交赔偿单，事故单单号[{}],赔偿单号[{}]----保存结果[{}]", dto.getAccidentInfoDTO().getAccidentNo(),orderNo,bool);
        return bool;
    }

    private boolean savePax(List<CompensationPaxInfoDTO> listPaxDTO, Long orderId) {
        List<CompensationPaxInfoDO> list = new ArrayList<CompensationPaxInfoDO>();
        for (CompensationPaxInfoDTO paxInfoDTO :
                listPaxDTO) {
            paxInfoDTO.setReceiveStatus(RECEIVE_STATUS_UNCLAIMED);
            paxInfoDTO.setOrderId(orderId);
            list.add(ObjectUtils.copyBean(paxInfoDTO, CompensationPaxInfoDO.class));
        }
        boolean bool = paxInfoService.saveBatch(list);
        return bool;
    }

    private boolean saveRuleRecord(List<CompensationRuleRecordDTO> listRuleRecordDTO, Long orderId, Long accidentId) {
        List<CompensationRuleRecordDO> list = new ArrayList<CompensationRuleRecordDO>();
        for (CompensationRuleRecordDTO ruleRecordDTO :
                listRuleRecordDTO) {
            ruleRecordDTO.setOrderId(orderId);
            ruleRecordDTO.setAccidentId(accidentId);
            list.add(ObjectUtils.copyBean(ruleRecordDTO, CompensationRuleRecordDTO.class));
        }
        boolean bool = ruleRecordService.saveBatch(list);
        return bool;
    }

    private BigDecimal calculateCompensationSum(List<CompensationRuleRecordDTO> listRuleRecordDTO, List<CompensationPaxInfoDTO> listPaxDTO) {
        CompensationRuleRecordDTO eCompensationRuleRecordDTO = null;
        CompensationRuleRecordDTO bCompensationRuleRecordDTO = null;
        CompensationCalDroolsRuleDTO compensationCalDroolsRuleDTO = new CompensationCalDroolsRuleDTO();
        for (CompensationRuleRecordDTO ruleRecordDTO : listRuleRecordDTO) {
            if (StringUtils.isNotBlank(ruleRecordDTO.getClassType())) {
                if (E_CLASS_TYPE.equals(ruleRecordDTO.getClassType())) {
                    eCompensationRuleRecordDTO = ruleRecordDTO;
                }
                if (B_CLASS_TYPE.equals(ruleRecordDTO.getClassType())) {
                    bCompensationRuleRecordDTO = ruleRecordDTO;
                }
            }
        }
        compensationCalDroolsRuleDTO.setBCompensationRuleRecordDTO(bCompensationRuleRecordDTO);
        compensationCalDroolsRuleDTO.setECompensationRuleRecordDTO(eCompensationRuleRecordDTO);
        compensationCalDroolsRuleDTO.setCode(CompensateDroolsRuleCodeEnum.IRREGULAR_FLIGHT.getValue());
        compensationCalDroolsRuleDTO.setListPaxDTO(listPaxDTO);
        try {
            executeRule(compensationCalDroolsRuleDTO);
        } catch (UnsupportedEncodingException e) {
            log.error("【aps-compensation-impl】创建赔偿单时，计算金额异常，listRuleRecordDTO：【{}】, listPaxDTO:【{}】", JSONUtil.toJsonStr(listRuleRecordDTO), JSONUtil.toJsonStr(listPaxDTO), e);
        }
        // 总金额
        return compensationCalDroolsRuleDTO.getSumMoney();
    }

    /**
     * Title：executeRule <br>
     * Description：执行规则 <br>
     * author：王磊 <br>
     * date：2021/11/11 14:31 <br>
     *
     * @param compensationCalDroolsRuleDTO <br>
     * @return <br>
     */
    private void executeRule(CompensationCalDroolsRuleDTO compensationCalDroolsRuleDTO) throws UnsupportedEncodingException {
        //获取配置的商务舱参数
        String[] bMainClasssign = ConfigUtil.get("B_MAIN_CLASSSIGN").
                getValues().get(0).getConfigValue().split(",");
        String[] bClasssign = ConfigUtil.get("B_CLASSSIGN").
                getValues().get(0).getConfigValue().split(",");

        long startTime = System.currentTimeMillis();
        log.info("【aps-compensation-impl】耗时统计——规则引擎->赔付计算规则耗时统计开始-------");
        KieSession kSession = droolsRuleService.getKieSessionFromDrl(compensationCalDroolsRuleDTO.getCode());
        log.info("【aps-compensation-impl】耗时统计——规则引擎->创建出的kSession，当前（非累积）耗时：【{}】-------", (System.currentTimeMillis() - startTime));
        kSession.insert(compensationCalDroolsRuleDTO);
        kSession.setGlobal("bClasssign", bClasssign);
        kSession.setGlobal("bMainClasssign", bMainClasssign);
        int count = kSession.fireAllRules();
        log.info("【aps-compensation-impl】耗时统计——规则引擎->装载规则执行对象以及执行规则运算，当前（非累积）耗时：【{}】，count:【{}】-------", (System.currentTimeMillis() - startTime), count);
    }

    /**
     * Title：createCompensationOrderNo <br>
     * Description：构建赔偿单编号 <br>
     * author：王磊 <br>
     * date：2021/11/11 14:31 <br>
     *
     * @param accidentNo
     * @param accidentId <br>
     * @return <br>
     */
    private String createCompensationOrderNo(String accidentNo, Long accidentId) {
        StringBuffer orderNo = new StringBuffer(ORDER_NO_PREFIX);
        if (StringUtils.isNotEmpty(accidentNo)) {
            orderNo.append(accidentNo.substring(3, accidentNo.length()));
            Integer count = getBaseMapper().findOrderCountByAccidentId(accidentId);
            count += 1;
            orderNo.append(String.format("%04d", count));
        }
        return orderNo.toString();
    }

    private String createCompensationOrderNo(String accidentType, String belongAirline) {
        return CompensationOrderNoUtils.getOrderNumber(UserContext.getCurrentUser().getTenantCode(),CompensationOrderNoUtils.getTypeCode(accidentType));
    }

    @Override
    public CompensationOrderDetailsVO findById(Long id) {
        String userId = String.valueOf(UserContext.getUserId());
        return getBaseMapper().findById(id,userId);
    }

    @Override
    public int takeEffect(Long orderId,String userName) {
        CompensationOrderInfoDTO dto = new CompensationOrderInfoDTO();
        dto.setIssUser(userName);
        dto.setStatus(CompensateStatusEnum.TAKE_EFFECT.getKey());
        dto.setId(orderId);
        dto.setRelease(ApsUserUtils.getCreatedBy());
        return getBaseMapper().upStatusById(dto);
    }

    @Override
    public int close(Long orderId,String userName) {
        LambdaQueryWrapper<CompensationOrderInfoDO> wrapper = new LambdaQueryWrapper<CompensationOrderInfoDO>().eq(CompensationOrderInfoDO::getId, orderId);
        CompensationOrderInfoDO compensationOrderInfoDO = getBaseMapper().selectOne(wrapper);
        CompensationOrderInfoDTO dto = new CompensationOrderInfoDTO();
        dto.setCloseUser(ApsUserUtils.getCreatedBy());
        dto.setCloseTime(LocalDateTime.now());
        //补偿单状态为生效转关闭，设置为5
        if(CompensateStatusEnum.TAKE_EFFECT.getKey().equals(compensationOrderInfoDO.getStatus())){
            dto.setStatus(CompensateStatusEnum.CLOSE.getKey());
        }
        //审核通过转关闭设置为8
        else{
            dto.setStatus(CompensateStatusEnum.AUDIT_PASS_CLOSE.getKey());
        }
        dto.setId(orderId);
        int result = getBaseMapper().upStatusById(dto);
        //关闭操作后进行事故单结案判断
        getBaseMapper().upAccidentStatus(orderId);

        //关闭赔偿单，对该赔偿单下未支付状态的申领单支付数据为冻结。
        payRecordService.freezePayOrder(orderId,null,"1");

        return result;
    }

    @Override
    public List<CompensationOrderAddTipsVO> getSameTypeOrders(String flightDate, String flightNo, String accidentType,Long orderId) {
        return getBaseMapper().getSameTypeOrders(flightDate,flightNo,accidentType,orderId);
    }

    @Override
    public CompensationOrderEditEchoVO getOrderEditEchoById(Long orderId) {
        //复用赔偿单详情查询信息
        CompensationOrderDetailsVO orderDetailsVO = getBaseMapper().findById(orderId,null);
        CompensationOrderEditEchoVO editEchoVO = ObjectUtils.copyBean(orderDetailsVO, CompensationOrderEditEchoVO.class);
        return editEchoVO;
    }

    @Override
    public boolean updCompensationOrderStatus(Long orderId, String status) {
        AccidentCompensationDTO compensation = compensationFactory.createByOrderId(String.valueOf(orderId));
        return compensationOrderStatusService.changeStatus(compensation,status);
    }

    @Override
    public List<PaxReceiveRecordVO> findPaxReceiveRecord(Long orderId,String paxId) {
        String nameAndNo = getBaseMapper().getNameAndNoByPaxId(paxId);
        String[] s = nameAndNo.split(",");
        String name = s[0];
        String no =s[1];
        log.info("【aps-compensation-impl】查看旅客申领信息，查询旅客信息:姓名【{}】，加密后证件号【{}】",name,no);
        return getBaseMapper().findPaxReceiveRecord(orderId,paxId,name,no);
    }

    /**
     * Title：deleteOrderInfo  <br>
     * Description： 删除赔偿单关联的 信息<br>
     * author：傅欣荣 <br>
     * date：2021/11/10 13:52 <br>
     *
     * @param
     * @return
     */
    private void deleteOrderInfo(Long orderId) {
        //删除赔偿单【驳回草稿情况下编辑删除】
        baseMapper.updRemoveOrderByStatus(orderId);
        Map<String, Object> columnMap = new HashMap<>();
        columnMap.put("order_id", orderId);
        //删除旅客
        paxInfoService.removeByMap(columnMap);
        //删除赔偿标准
        ruleRecordService.removeByMap(columnMap);
        //删除航班
        flightInfoService.removeByMap(columnMap);
    }

    /**
     * Title： updAccidentStatus<br>
     * Description： 赔偿单新建变更事故单状态 <br>
     * author：傅欣荣 <br>
     * date：2021/11/23 13:47 <br>
     * @param
     * @return
     */

    private void updAccidentStatus(CompensationSyntheticalSaveDTO dto){


        String accidentStatus = "";
        //事故单状态为待处理 、 已结案 ，新增赔偿单修改为处理中
        if(AccidentStatusEnum.CASE_CLOSED.getValue().equals(dto.getAccidentInfoDTO().getAccidentStatus())
                || AccidentStatusEnum.TODO.getValue().equals(dto.getAccidentInfoDTO().getAccidentStatus())){
            accidentStatus = AccidentStatusEnum.PROCESS.getValue();
        }
        //只有新建非草稿状态才会更新事故单
        if(StringUtils.isNotEmpty(accidentStatus)
                && CompensateStatusEnum.AUDIT_PROCESS.getKey().equals(dto.getOrderInfoDTO().getStatus())){
            log.info("【aps-compensation-impl】提交赔偿单，新建时->更新事故单状态为[{}]。事故单原状态-[{}] 事故单号-[{}],赔偿单号(应该是null)-[{}]",
                    accidentStatus, dto.getAccidentInfoDTO().getAccidentStatus(),dto.getAccidentInfoDTO().getAccidentNo(),
                    dto.getOrderInfoDTO().getOrderNo());
            FlightAccidentInfoDO accidentDo = accidentInfoService.getById(dto.getAccidentInfoDTO().getId());
            accidentDo.setAccidentStatus(accidentStatus);
            accidentDo.setUpdatedTime(LocalDateTime.now());
            accidentDo.setUpdatedBy(ApsUserUtils.getCreatedBy());
            accidentInfoService.updateById(accidentDo);
        }
    }

    /**
     * Title： deleteAuditInfo<br>
     * Description： 删除临时审批记录<br>
     * author：傅欣荣 <br>
     * date：2021/12/23 16:54 <br>
     * @param
     * @return
     */
    @Override
    public void deleteAuditInfo(Long orderId){
        if(StringUtils.isNotEmpty(String.valueOf(orderId))){
            //删除待审核记录
            CompensationAuditInfoDO auditInfoDO = new CompensationAuditInfoDO();
            auditInfoDO.setOrderId(orderId);
            compensationAuditInfoService.getBaseMapper().delete(Wrappers.lambdaQuery(auditInfoDO));
            redisUtil.del(StrUtil.format(CompensationConstant.COMPENSATION_AUDITOR_KEY,orderId));
        }
    }

    @Override
    public CompensationOrderInfoDO createCompensationOrder(CompensationOrderInfoDO compensationOrderInfoDO) {
        compensationOrderInfoDO.setOrderNo(createCompensationOrderNo(compensationOrderInfoDO.getAccidentType(),UserContext.getUser().getTenantCode()));
        this.baseMapper.insert(compensationOrderInfoDO);
        return compensationOrderInfoDO;
    }

    @Override
    public List<CompensationOrderInfoDO> findOverdueOrder() {
        return compensationOrderInfoMapper.findOverdueOrder();
    }

    @Override
    public void updateOverdueOrder(List<Long> orderIds,String status) {
        this.getBaseMapper().updateOverdueOrder(orderIds,status);
    }

    @Override
    public List<CompensationOrderInfoDO> findCloseAccidentOrder() {
        return this.getBaseMapper().findCloseAccidentOrder();
    }

    @Override
    public void updateCreatedBy(String compensationId, String currentUserId) {
        compensationOrderInfoMapper.updateOrderCreatedBy(compensationId,currentUserId);
    }

    @Override
    public Long getTenantIdByCode(String tenantCode) {
        return compensationOrderInfoMapper.getTenantIdByCode(tenantCode);
    }

    @Override
    public String getTenantCodeById(Long tenantId) {
        Map<String, Object> tenant = compensationOrderInfoMapper.getTenantById(tenantId);
        return (String) tenant.get("TENANT_CODE");
    }

    @Override
    public String getTenantCompanyNameById(Long tenantId) {
        Map<String, Object> tenant = compensationOrderInfoMapper.getTenantById(tenantId);
        return (String) tenant.get("COMPANY_NAME");
    }

    @Override
    public String getTenantNameById(Long tenantId) {
        Map<String, Object> tenant = compensationOrderInfoMapper.getTenantById(tenantId);
        return (String) tenant.get("TENANT_NAME");
    }

    @Override
    public void verifyAirportBusinessPrivilege(String airportCode, String businessTypeCode) {
        //1.建补偿单 新增判断《当前服务航站，是否已经授权给其他【机场端】，如果已经授权，不能进行创建补偿单。只能由【机场端】创建补偿端》
        LoginUserDetails userDetails = UserContext.getUser();
        //获取航站（三字码），授权情况
        BusinessPrivilegeDTO detailByAirport = businessPrivilegeService.getDetailByAirport(airportCode);
        //当前航司租户code 是否已授权了补偿航站给机场
        if(null != detailByAirport && userDetails.getTenantCode().equals(detailByAirport.getGrantorCode())){
            //获取航站-具体业务授权情况
            List<BusinessPrivilegeItemDTO> businessPrivilegeItemList = detailByAirport.getBusinessPrivilegeItemList();
            //如果业务已授权，不能创建补偿单
            if(CollectionUtils.isNotEmpty(businessPrivilegeItemList)){
                List<BusinessPrivilegeItemDTO> privilegeItemDTOList = businessPrivilegeItemList.stream()
                        .filter(item -> businessTypeCode.equals(item.getBusinessTypeCode()))
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(privilegeItemDTOList)){
                    //该业务已被授权给机场，航司不能操作新建补偿单
                    throw new BusinessException(CompensationException.COMPENSATION_SAVE_BUSINESS_PRIVILEGE_ERROR);
                }
            }
        }
    }

    @Override
    public List<CompensationOrderInfoDO> findNeedRefreshWorkflowUserInfoOrder() {
        return compensationOrderInfoMapper.findNeedRefreshWorkflowUserInfoOrder();
    }

}