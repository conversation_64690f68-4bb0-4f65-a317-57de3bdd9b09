package com.swcares.aps.compensation.impl.util;

import com.swcares.aps.cpe.coordinate.model.constant.CoordinateCenterErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.components.oauth2.SecurityUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname TokenUtils
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/26 09:49
 * @Version 1.0
 */
@Slf4j
public class TokenUtils {
    private static String token;
    private static LocalDateTime expireTime = LocalDateTime.now();
    public static synchronized String getToken(){
        if (token == null || LocalDateTime.now().plusMinutes(5).isAfter(expireTime)){
            Map<String, String> params = new HashMap<>(2);
            params.put("grant_type", "client_credentials");
            Map<String, String> result = SecurityUtils.getToken(params);
            if (result == null) {
                log.error("无法获取token");
                //抛出异常
                throw new BusinessException(CoordinateCenterErrors.GET_TOKEN_FAILED);
            }
            token = result.get("access_token");
            if (token == null){
                log.error("无法获取token");
                //抛出异常
                throw new BusinessException(CoordinateCenterErrors.GET_TOKEN_FAILED);
            }
            long expireIn = Long.parseLong(String.valueOf(result.get("expires_in")));
            expireTime = LocalDateTime.now().plusSeconds(expireIn);
        }
        return token;
    }
}
