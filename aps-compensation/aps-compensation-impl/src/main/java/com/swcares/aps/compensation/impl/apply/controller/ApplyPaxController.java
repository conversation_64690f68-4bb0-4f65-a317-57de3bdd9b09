package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.dto.UpdateApplyPaxStatusDTO;
import com.swcares.aps.compensation.model.apply.vo.CompensationInfoVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * ClassName：com.swcares.compensation.controller.ApplyPaxController <br>
 * Description：航延补偿申领旅客信息表 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/apply/applyPax")
@Api(tags = "航延补偿申领旅客信息表接口")
@ApiVersion(value = "申领单接口 v1.0")
public class ApplyPaxController extends BaseController {
    @Autowired
    private ApplyPaxService applyPaxService;

    @PostMapping("/authPax")
    @ApiOperation(value = "验证本人领取信息")
    public BaseResult<Object> authPax(@RequestBody @Valid AuthPaxDTO dto) {
        //需要判断资金授权
        dto.setGrantBankroll("1");
        applyPaxService.authPax(dto);
        return ok();
    }

    @GetMapping("/sendSMS")
    @ApiOperation(value = "发送短信验证码")
    public BaseResult<Object> sendSMS(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum) {
        applyPaxService.sendSMS(phoneNum);
        return ok();
    }

    @GetMapping("/verificationSMS")
    @ApiOperation(value = "验证短信验证码")
    public BaseResult<Object> verificationSMS(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum,@ApiParam(value = "验证码", required = true) @RequestParam String authCode) {
        applyPaxService.verificationSMS(phoneNum,authCode);
        return ok();
    }

    @PostMapping("/findCompensationOrder")
    @ApiOperation(value = "获取旅客赔偿单详情")
    public BaseResult<CompensationInfoVO> findCompensationOrder(@RequestBody @Valid AuthPaxDTO dto) {
        //需要判断资金授权
        dto.setGrantBankroll("1");
        return ok(applyPaxService.findCompensationOrderSmsVerify(dto));
    }

    @PostMapping("/updateApplyPaxStatus")
    @ApiOperation(value = "通过ID修改旅客申领单的状态和天蝎错误信息")
    public BaseResult<Object> updateApplyPaxStatus(@RequestBody @Valid UpdateApplyPaxStatusDTO dto) {
        return ok(applyPaxService.updateApplyPaxStatus(dto));
    }
}
