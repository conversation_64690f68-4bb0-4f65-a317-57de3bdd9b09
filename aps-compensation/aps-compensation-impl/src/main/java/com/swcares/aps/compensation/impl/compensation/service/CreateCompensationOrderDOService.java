package com.swcares.aps.compensation.impl.compensation.service;

import com.alibaba.fastjson.JSON;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoVO;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.overbook.constant.OverBookException;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationAddAndEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialAddCommandDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.enums.CompensationAccidentTypeEnum;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.component.dict.util.DictUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName：CreateCompensationOrderDOService
 * @Description：创建CompensationOrderDO service
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/14 13:09
 * @version： v1.0
 */
@Component
@Slf4j
public class CreateCompensationOrderDOService {

    private final static String COMPENSATE_TYPE_CASH_DICT_ID="1"; //现金
    private final static String COMPENSATE_TYPE_VIRTUAL_DICT_ID="2"; //虚拟
    private final static String COMPENSATE_TYPE_MATERIAL_DICT_ID ="3"; //实物
    private final static String ENSURE_TYPE_DICT_NAME="ensure_type"; //保障服务 字典类型名称
    @Autowired
    private CompensationBasicDataService compensationBasicDataService;

    public CompensationOrderInfoDO createCashByBaggageAccident(CompensationCashAddCommandDTO command, BaggageAccidentInfoDO accident, List<FocFlightInfoVO> flightInfoVOS) {

        CompensationOrderInfoDO compensationOrderInfoDO = createByBaggageAccident(command,
                accident,
                flightInfoVOS,
                command.getCompensationAllMoney(),
                COMPENSATE_TYPE_CASH_DICT_ID);
        return compensationOrderInfoDO;
    }

    public CompensationOrderInfoDO createCashByBaggageAccidentManualInput(CompensationCashAddCommandDTO command, BaggageAccidentInfoDO accident) {

        CompensationOrderInfoDO compensationOrderInfoDO = createByBaggageAccidentManualInput(command,
                accident,
                command.getCompensationAllMoney(),
                COMPENSATE_TYPE_CASH_DICT_ID);
        return compensationOrderInfoDO;
    }

    public CompensationOrderInfoDO createMaterialInByBaggageAccident(CompensationMaterialAddCommandDTO command, BaggageAccidentInfoDO accident, List<FocFlightInfoVO> flightInfoVOS) {
        CompensationOrderInfoDO compensationOrderInfoDO = createByBaggageAccident(command,
                accident,
                flightInfoVOS,
                null,
                COMPENSATE_TYPE_MATERIAL_DICT_ID);
        return compensationOrderInfoDO;
    }

    public CompensationOrderInfoDO createMaterialInByBaggageAccidentManualInput(CompensationMaterialAddCommandDTO command, BaggageAccidentInfoDO accident) {
        CompensationOrderInfoDO compensationOrderInfoDO = createByBaggageAccidentManualInput(command,
                accident,
                null,
                COMPENSATE_TYPE_MATERIAL_DICT_ID);
        return compensationOrderInfoDO;
    }

    private CompensationOrderInfoDO createByBaggageAccident(CompensationAddAndEditCommandDTO command, BaggageAccidentInfoDO accident, List<FocFlightInfoVO> flightInfoVOS, BigDecimal sumMoney,String compensateType){
        List<String> segments = flightInfoVOS.stream().map(FocFlightInfoVO::getSegment).collect(Collectors.toList());
        log.info("【创建异常行李补偿单-系统录入createByBaggageAccident】事故单号：[{}] ,旅客航段信息:[{}]",accident.getAccidentNo(), JSON.toJSONString(segments));
        String fullSegment= segments.get(0);
        if(segments.size()>1){
            for(int i=1;i<segments.size();i++){
                String [] splits = segments.get(i).split("-");
                fullSegment+="-"+ splits[1];
            }
        }
        //旅客信息查询
        PassengerQueryDTO queryDTO = new PassengerQueryDTO();
        queryDTO.setPaxId(accident.getPaxId());
        List<PassengerBasicInfoVO> passengerInfo = compensationBasicDataService.getPassengerInfo(queryDTO);
        if(ObjectUtils.isEmpty(passengerInfo)){
            throw new BusinessException(OverBookException.NOT_FOUND_PAX);
        }
        PassengerBasicInfoVO passengerBasicInfoVO = passengerInfo.get(0);

        CompensationOrderInfoDO compensationOrderInfoDO=new CompensationOrderInfoDO();
        compensationOrderInfoDO.setAccidentId(accident.getId());
        compensationOrderInfoDO.setAccidentNo(accident.getAccidentNo());
        compensationOrderInfoDO.setAccidentType(CompensationAccidentTypeEnum.ABNORMAL_BAGGAGE.getKey());
        compensationOrderInfoDO.setAccidentSubType(accident.getType());
        compensationOrderInfoDO.setChoiceSegment(passengerBasicInfoVO.getSegment());
        compensationOrderInfoDO.setChoiceSegmentCh(passengerBasicInfoVO.getSegmentCh());
        compensationOrderInfoDO.setCompensateType(compensateType);
        compensationOrderInfoDO.setCompensateSubType(command.getCompensateSubType());
        compensationOrderInfoDO.setCompensateStandard(command.getCompensateStandard());
        compensationOrderInfoDO.setEnsureType(String.valueOf(getEnsureTypeDict(command.getEnsureType()).getDictItem()));
        compensationOrderInfoDO.setFlightId(passengerBasicInfoVO.getFlightId());
        compensationOrderInfoDO.setFlightDate(accident.getPaxFlightDate());
        compensationOrderInfoDO.setFlightNo(accident.getPaxFlightNo());

        compensationOrderInfoDO.setFullSegment(fullSegment);
        compensationOrderInfoDO.setRemark(command.getCompensationReason());
        compensationOrderInfoDO.setServiceCity(command.getServiceCity());
        compensationOrderInfoDO.setStatus(CompensateStatusEnum.DRAFT.getValue());
        compensationOrderInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        compensationOrderInfoDO.setCreatedTime(LocalDateTime.now());
        compensationOrderInfoDO.setSumMoney(sumMoney);
        compensationOrderInfoDO.setBelongAirline(UserContext.getCurrentUser().getTenantCode());
        compensationOrderInfoDO.setBelongAirlineAbbr(UserContext.getCurrentUser().getTenantName());
        compensationOrderInfoDO.setSource(accident.getAccidentSource());
        return compensationOrderInfoDO;
    }

    private CompensationOrderInfoDO createByBaggageAccidentManualInput(CompensationAddAndEditCommandDTO command, BaggageAccidentInfoDO accident, BigDecimal sumMoney,String compensateType){
        log.info("【创建异常行李补偿单-手工录入,createByBaggageAccident】事故单号：[{}] ,旅客航段信息:[{}]",accident.getAccidentNo(), accident.getPaxSegment());
        String choiceSegment = extractAirportCodes(accident.getPaxSegment());
        CompensationOrderInfoDO compensationOrderInfoDO=new CompensationOrderInfoDO();
        compensationOrderInfoDO.setAccidentId(accident.getId());
        compensationOrderInfoDO.setAccidentNo(accident.getAccidentNo());
        compensationOrderInfoDO.setAccidentType(CompensationAccidentTypeEnum.ABNORMAL_BAGGAGE.getKey());
        compensationOrderInfoDO.setAccidentSubType(accident.getType());
        compensationOrderInfoDO.setChoiceSegment(choiceSegment);
        compensationOrderInfoDO.setChoiceSegmentCh(accident.getPaxSegment());
        compensationOrderInfoDO.setCompensateType(compensateType);
        compensationOrderInfoDO.setCompensateSubType(command.getCompensateSubType());
        compensationOrderInfoDO.setCompensateStandard(command.getCompensateStandard());
        compensationOrderInfoDO.setEnsureType(String.valueOf(getEnsureTypeDict(command.getEnsureType()).getDictItem()));
        compensationOrderInfoDO.setFlightId(accident.getPaxFlightId());
        compensationOrderInfoDO.setFlightDate(accident.getPaxFlightDate());
        compensationOrderInfoDO.setFlightNo(accident.getPaxFlightNo());

        compensationOrderInfoDO.setFullSegment(choiceSegment);
        compensationOrderInfoDO.setRemark(command.getCompensationReason());
        compensationOrderInfoDO.setServiceCity(command.getServiceCity());
        compensationOrderInfoDO.setStatus(CompensateStatusEnum.DRAFT.getValue());
        compensationOrderInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        compensationOrderInfoDO.setCreatedTime(LocalDateTime.now());
        compensationOrderInfoDO.setSumMoney(sumMoney);
        compensationOrderInfoDO.setBelongAirline(UserContext.getCurrentUser().getTenantCode());
        compensationOrderInfoDO.setBelongAirlineAbbr(UserContext.getCurrentUser().getTenantName());
        compensationOrderInfoDO.setSource(accident.getAccidentSource());
        compensationOrderInfoDO.setInputSource(accident.getInputSource());
        return compensationOrderInfoDO;
    }

    private DictCacheVO getEnsureTypeDict(String dictValue){
        return DictUtil.getDictData(ENSURE_TYPE_DICT_NAME,null).stream()
                .collect(Collectors.toMap(DictCacheVO::getDictItem,v->v))
                .get(dictValue);

    }


    private static String extractAirportCodes(String input) {

        //拉萨贡嘎LXA-成都双流CTU 获取LXA-CTU
        String[] split = input.split("-");

        return split[0].substring(split[0].length() - 3) + "-" + split[1].substring(split[1].length() - 3);
    }

}
