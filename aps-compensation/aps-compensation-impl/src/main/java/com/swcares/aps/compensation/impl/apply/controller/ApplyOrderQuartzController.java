package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.aps.compensation.impl.apply.service.ApplyOrderQuartzService;
import com.swcares.aps.compensation.model.apply.dto.ApplyOrderPaxStatusUpdDTO;
import com.swcares.aps.compensation.model.apply.dto.OrderPaxStatusPayUpdDTO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyOrderPayVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.utils.lang.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * ClassName：com.swcares.compensation.controller.ApplyOrderController <br>
 * Description：航延补偿申领单定时任务提供接口<br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
//@RestController
@RequestMapping("/apply/order/task")
@Api(tags = "航延补偿申领定时任务接口")
@ApiVersion(value = "申领单接口 v1.0")
public class ApplyOrderQuartzController extends BaseController {

    @Autowired
    private ApplyOrderQuartzService applyOrderQuartzService;

    //@PostMapping("/findUnpaidOrderInfo")
    @ApiOperation(value = "获取申领单支付数据")
    public BaseResult<List<ApplyOrderPayVO>> findUnpaidOrderInfo() {
        return ok(applyOrderQuartzService.findUnpaidOrderInfo());
    }


    //@PostMapping("/saveRecord")
    @ApiOperation(value = "保存并更新支付记录表")
    public BaseResult<Object> saveRecord(@RequestBody PayRecordDO payRecordDO) {
        applyOrderQuartzService.updateById(payRecordDO);
        return ok();
    }


    //@PostMapping("/updatePayRecordById")
    @ApiOperation(value = "更新支付记录表根据id")
    public BaseResult<Object> updatePayRecordById(@RequestBody PayRecordDO payRecordDO) {
        applyOrderQuartzService.updatePayRecordById(payRecordDO);
        return ok();
    }


    //@PostMapping("/updOrderPaxStatus")
    @ApiOperation(value = "更新赔偿单旅客领取状态")
    public BaseResult<Object> updOrderPaxStatus(@RequestBody OrderPaxStatusPayUpdDTO dto){
        applyOrderQuartzService.updOrderPaxStatus(dto);
        return ok();
    }

    //@PostMapping("/updApplyOrderPaxStatus")
    @ApiOperation(value = "更新申领单旅客领取状态")
    public BaseResult<Object> updApplyOrderPaxStatus(@RequestBody ApplyOrderPaxStatusUpdDTO dto){
        applyOrderQuartzService.updApplyOrderPaxStatus(dto);
        return ok();
    }


    //@PostMapping("/updApplyOrderInfo")
    @ApiOperation(value = "更新申领单领取时间")
    public BaseResult<Object> updApplyOrderInfo(@RequestParam String applyCode, @RequestParam(value = "receieveTime" ,required = false)  String receieveTime){
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.PTN_YMD_HMS);
        final LocalDateTime parse = LocalDateTime.parse(receieveTime, dateTimeFormatter);
        applyOrderQuartzService.updApplyOrderInfo(applyCode,parse);
        return ok();
    }


}
