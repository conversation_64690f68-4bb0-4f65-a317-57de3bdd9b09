package com.swcares.aps.compensation.impl.blacklist.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.blacklist.dto.BlackListDto;
import com.swcares.aps.compensation.model.blacklist.entity.BlackListDO;

/**
 * @ClassName：BlackListService
 * @Description：黑名单管理接口
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/10/14 15:57
 * @version： v1.0
 */
public interface BlackListService extends IService<BlackListDO> {

    Page<BlackListDO> list(BlackListDto dtos);
    int saveBlackList(BlackListDto dtos);
    int updateBlackList(BlackListDto dtos);
}
