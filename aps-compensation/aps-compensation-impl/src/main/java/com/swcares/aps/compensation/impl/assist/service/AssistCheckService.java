package com.swcares.aps.compensation.impl.assist.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.assist.dto.CheckInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AssistCheckService
 * @projectName aps
 * @description: 协助领取的信息校验
 * @date 2022/1/20 13:05
 */
public interface AssistCheckService {
    /**
    * @title getPassengers
    * @description 协助领取信息校验
    * <AUTHOR>
    * @date 2022/1/20 13:13
    * @param dto
    * @return
    */
    Object checked(CheckInfoDTO dto);
}
