package com.swcares.aps.compensation.impl.assist.utils;

import com.swcares.aps.compensation.impl.assist.constant.AssistApplyTips;
import com.swcares.components.msg.dto.CustomerMessageDepositoryDTO;
import com.swcares.components.msg.enums.ReceiveModeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title: SendSMSUtils
 * @projectName aps
 * @description:
 * @date 2022/2/16 11:19
 */
public class SendSMSUtils {
    public static List<CustomerMessageDepositoryDTO> sendSMSDTO(List<String> phones,String msg){
        List<CustomerMessageDepositoryDTO> list = new ArrayList<>();
        for (String phone : phones) {
            CustomerMessageDepositoryDTO dto = new CustomerMessageDepositoryDTO();
            dto.setMsgContent(msg);
            dto.setReceiveNum(phone);
            dto.setReceiveMode(ReceiveModeEnum.SMS);
            dto.setSystemCode(AssistApplyTips.SYSTEM_CODE);
            dto.setBusinessType(AssistApplyTips.BUSINESS_TYPE);
            dto.setMasterId(AssistApplyTips.MASTER_ID);
            list.add(dto);
        }
        return list;
    }
}
