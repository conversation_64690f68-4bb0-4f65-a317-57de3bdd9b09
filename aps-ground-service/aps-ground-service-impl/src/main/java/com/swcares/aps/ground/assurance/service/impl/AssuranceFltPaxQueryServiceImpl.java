package com.swcares.aps.ground.assurance.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.basic.data.remoteapi.FltPassengerDataSourceServiceFactory;
import com.swcares.aps.basic.data.remoteapi.api.FlightBasicDataService;
import com.swcares.aps.basic.data.remoteapi.api.PassengerBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightInfoDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightUnitInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.ground.assurance.mapper.PassengerCategoryConfigureMapper;
import com.swcares.aps.ground.assurance.service.AssuranceFltPaxQueryService;
import com.swcares.aps.ground.models.assurance.dto.PaxQueryDTO;
import com.swcares.aps.ground.models.assurance.vo.AssurancePassengerVO;
import com.swcares.aps.ground.models.assurance.vo.ProtectFltInfoVO;
import com.swcares.aps.ground.models.passengerCatefory.PassengerCategoryConfigureDepository;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName：FlightPassengerQueryServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/22 10:17
 * @version： v1.0
 */
@Service
@Slf4j
public class AssuranceFltPaxQueryServiceImpl implements AssuranceFltPaxQueryService {

    @Autowired
    FltPassengerDataSourceServiceFactory fltPassengerDataSourceServiceFactory;

    @Autowired
    PassengerCategoryConfigureMapper passengerCategoryConfigureMapper;

    @Override
    public ProtectFltInfoVO getFltInfo(String flightNo, String flightDate) {
        FlightBaseQueryDTO flightBaseQueryDTO = new FlightBaseQueryDTO();
        flightBaseQueryDTO.setFlightDate(flightDate);
        flightBaseQueryDTO.setFlightNo(flightNo);

        FlightBasicDataService flightService = fltPassengerDataSourceServiceFactory.getFlightService();
        List<FlightBasicnfoVO> flightList = flightService.getFlightList(flightBaseQueryDTO);
        ProtectFltInfoVO protectFltInfoVO = new ProtectFltInfoVO();

        if (flightList == null || flightList.isEmpty()) {
            return protectFltInfoVO;
        }
        List<FlightBasicnfoVO> sortedList = flightList.stream().sorted(Comparator.comparing(FlightBasicnfoVO::getStd)).collect(Collectors.toList());
        List<String> segments = new ArrayList<>();
        Set<String> codes = new HashSet<>();

        sortedList.forEach(flight -> {
            codes.add(flight.getPod());
            codes.add(flight.getPoa());
            addIfNotBlank(codes, flight.getAlternateAirport());
            addIfNotBlank(codes, flight.getAlternateAirport2());
            segments.add(flight.getSegment());
        });

        if (sortedList.size() == 2) {
            segments.add(sortedList.get(0).getPod() + "-" +sortedList.get(1).getPoa());
        }

        protectFltInfoVO.setSegments(segments);
        protectFltInfoVO.setCodes(new ArrayList<>(codes));

        return protectFltInfoVO;
    }

    @Override
    public List<AssurancePassengerVO> queryPax(PaxQueryDTO dto) {
        PassengerQueryDTO passengerQueryDTO = new PassengerQueryDTO();
        if (StrUtil.isNotBlank(dto.getPaxName())){
            dto.setPaxName("%"+dto.getPaxName()+"%");
        }
        BeanUtils.copyProperties(dto, passengerQueryDTO);

        PassengerBasicDataService passengerService = fltPassengerDataSourceServiceFactory.getPassengerService();
        List<PassengerBasicInfoVO> passengerInfo = passengerService.getPassengerInfo(passengerQueryDTO);
        if (passengerInfo == null || passengerInfo.isEmpty()) {
            return Collections.emptyList();
        }
        List<AssurancePassengerVO> result = new ArrayList<>();
        LambdaQueryWrapper<PassengerCategoryConfigureDepository> wrapper = Wrappers.lambdaQuery(PassengerCategoryConfigureDepository.class);
        wrapper.eq(PassengerCategoryConfigureDepository::getState, "1");
        wrapper.eq(PassengerCategoryConfigureDepository::getAirCode, UserContext.getCurrentUser().getTenantCode());
        List<PassengerCategoryConfigureDepository> categoryConfigure = passengerCategoryConfigureMapper.selectList(wrapper);
        Map<String, PassengerCategoryConfigureDepository> categoryMap;
        if (categoryConfigure!=null && !categoryConfigure.isEmpty()){
            categoryMap = categoryConfigure.stream().collect(Collectors.toMap(PassengerCategoryConfigureDepository::getCode, t->t));
        } else {
            categoryMap = null;
        }
        passengerInfo.forEach(pax -> {
            AssurancePassengerVO assurancePassengerVO = new AssurancePassengerVO();
            BeanUtils.copyProperties(pax, assurancePassengerVO);

            if (categoryMap != null && StringUtils.isNotBlank(pax.getCategoryCode())){
                List<String> categoryCodes = Arrays.stream(pax.getCategoryCode().split(","))
                        .filter(categoryMap::containsKey)
                        .collect(Collectors.toList());

                String categories = categoryCodes.stream().map(categoryMap::get).map(PassengerCategoryConfigureDepository::getCategory).collect(Collectors.joining(","));
                assurancePassengerVO.setCategoryCode(categories);
                String types = categoryCodes.stream().map(categoryMap::get).map(PassengerCategoryConfigureDepository::getType).collect(Collectors.joining(","));
                assurancePassengerVO.setCategoryPaxType(types);
            }
            result.add(assurancePassengerVO);
        });
        return result;
    }

    @Override
    public List<FlightUnitInfoVO> getFlightUnitInfo(List<FlightInfoDTO> flightInfoList) {
        if (flightInfoList == null || flightInfoList.isEmpty()){
            return Collections.emptyList();
        }
        List<FlightUnitInfoVO> result = new ArrayList<>();
        FlightBasicDataService flightService = fltPassengerDataSourceServiceFactory.getFlightService();
        flightInfoList.forEach(flightInfo -> result.addAll(flightService.getFlightUnitInfo(flightInfo)));
        return result.stream().distinct().collect(Collectors.toList());
    }

    private void addIfNotBlank(Set<String> codes, String airport) {
        if (StrUtil.isNotBlank(airport)) {
            codes.add(airport);
        }
    }

}
