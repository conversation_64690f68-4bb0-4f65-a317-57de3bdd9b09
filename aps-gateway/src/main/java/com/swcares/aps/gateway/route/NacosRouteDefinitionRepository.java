package com.swcares.aps.gateway.route;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionRepository;
import org.springframework.context.ApplicationEventPublisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Executor;

/**
 * @see org.springframework.cloud.gateway.config.GatewayAutoConfiguration
 * CachingRouteLocator实现了ApplicationEventPublisherAware接口
 * PropertiesRouteDefinitionLocator-->|配置文件加载初始化| CompositeRouteDefinitionLocator
 *  RouteDefinitionRepository-->|存储器中加载初始化| CompositeRouteDefinitionLocator
 *  DiscoveryClientRouteDefinitionLocator-->|注册中心加载初始化| CompositeRouteDefinitionLocator
 */
public class NacosRouteDefinitionRepository implements RouteDefinitionRepository {

    private static final Logger LOG = LoggerFactory.getLogger(NacosRouteDefinitionRepository.class);

    //更新路由
    private ApplicationEventPublisher publisher;

    //nacos 的配置信息
    private NacosConfigProperties nacosConfigProperties;

    private ConfigService configService;

    public NacosRouteDefinitionRepository(ApplicationEventPublisher publisher, NacosConfigProperties nacosConfigProperties) {
        this.publisher = publisher;
        this.nacosConfigProperties = nacosConfigProperties;
        addListener();
    }

    @Override
    public Flux<RouteDefinition> getRouteDefinitions() {
        try {
            if(configService == null){
                //增加Nocos 授权认证
                Properties properties = new Properties();
                properties.setProperty(PropertyKeyConst.SERVER_ADDR, nacosConfigProperties.getServerAddr());
                properties.setProperty(PropertyKeyConst.USERNAME, nacosConfigProperties.getUsername());
                properties.setProperty(PropertyKeyConst.PASSWORD, nacosConfigProperties.getPassword());
                configService = NacosFactory.createConfigService(properties);
            }
            String content = configService.getConfig("aps-gateway", "DEFAULT_GROUP", 5000);
            List<RouteDefinition> routeDefinitions = getListByStr(content);

            return Flux.fromIterable(routeDefinitions);
        } catch (NacosException e) {
            LOG.error("getRouteDefinitions by nacos error", e);
        }
        return Flux.fromIterable(new ArrayList<>());
    }

    /**
     * 添加Nacos监听
     */
    private void addListener() {
        try {
            if(configService == null){
                Properties properties = new Properties();
                properties.setProperty(PropertyKeyConst.SERVER_ADDR, nacosConfigProperties.getServerAddr());
                properties.setProperty(PropertyKeyConst.USERNAME, nacosConfigProperties.getUsername());
                properties.setProperty(PropertyKeyConst.PASSWORD, nacosConfigProperties.getPassword());
                configService = NacosFactory.createConfigService(properties);
            }
            configService.addListener("aps-gateway", "DEFAULT_GROUP", new Listener() {
                @Override
                public Executor getExecutor() {
                    return null;
                }
                @Override
                public void receiveConfigInfo(String configInfo) {
                    publisher.publishEvent(new RefreshRoutesEvent(this));
                }
            });
        } catch (NacosException e) {
            LOG.error("nacos-addListener-error", e);
        }
    }


    @Override
    public Mono<Void> save(Mono<RouteDefinition> route) {
        return null;
    }

    @Override
    public Mono<Void> delete(Mono<String> routeId) {
        return null;
    }

    /**
     * 解析json中的路由信息
     * @param content
     * @return
     */
    private List<RouteDefinition> getListByStr(String content) {
        if (StrUtil.isNotEmpty(content)) {
            return JSONUtil.toList(JSONUtil.parseArray(content), RouteDefinition.class);
        }
        return new ArrayList<>(0);
    }
}
