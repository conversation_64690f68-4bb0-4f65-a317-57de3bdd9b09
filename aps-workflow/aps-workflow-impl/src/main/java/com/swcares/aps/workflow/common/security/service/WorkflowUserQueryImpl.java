package com.swcares.aps.workflow.common.security.service;

import com.beust.jcommander.internal.Lists;
import com.swcares.aps.workflow.common.api.UCApi;
import com.swcares.aps.workflow.common.util.SpringBeanUtil;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.common.uc.dto.UserPagedDTO;
import com.swcares.common.uc.vo.UserVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.idm.api.User;
import org.flowable.idm.engine.impl.UserQueryImpl;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityImpl;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * ClassName：WorkflowUserQueryImpl <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/1 <br>
 * @version v1.0 <br>
 */
public class WorkflowUserQueryImpl extends UserQueryImpl {


    @Override
    public long executeCount(CommandContext commandContext) {
        return executeQuery(commandContext).size();
    }

    @Override
    public List<User> executeList(CommandContext commandContext) {
        return executeQuery(commandContext);
    }

    protected List<User> executeQuery(CommandContext commandContext) {
        if (StringUtils.isNotEmpty(getId())) {
            List<User> result = new ArrayList<>();
            User user = findById(getId());
            if (user != null) {
                result.add(user);
            }
            return result;

        } else if (StringUtils.isNotEmpty(getIdIgnoreCase())) {
            List<User> result = new ArrayList<>();
            User user = findById(getIdIgnoreCase());
            if (user != null) {
                result.add(user);
            }
            return result;

        } else if (StringUtils.isNotEmpty(getFullNameLike())) {
            return executeNameQuery(getFullNameLike());

        } else if (StringUtils.isNotEmpty(getFullNameLikeIgnoreCase())) {
            return executeNameQuery(getFullNameLikeIgnoreCase());

        } else if (StringUtils.isNotEmpty(getGroupId())) {
            return executeGroupQuery(Lists.newArrayList(getGroupId()));

        } else if (CollectionUtils.isNotEmpty(getGroupIds())) {

            return executeGroupQuery(getGroupIds());
        }else {

            return executeAllUserQuery();
        }
    }

    private List<User> executeGroupQuery( List<String> groupIds) {
        return new ArrayList<>();
    }

    protected List<User> executeNameQuery(final String name) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication==null){

            return Lists.newArrayList(createDefaultUser());
        }

        UCApi api = SpringBeanUtil.getBean(UCApi.class);
        UserPagedDTO userPagedDTO=new UserPagedDTO();
        userPagedDTO.setName(name);
        userPagedDTO.setPageSize(1000);
        PagedResult<List<UserVO>> page = api.getByPage(userPagedDTO);
        if(page.getTotalRecords()<=0){
            return Collections.EMPTY_LIST;
        }
        List list=new ArrayList<UserEntityImpl>((int)page.getTotalRecords());
        page.getData().forEach(e->{
            list.add(convertToUser(e));
        });
        return list;
    }

    protected List<User> executeAllUserQuery() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication==null){

            return Lists.newArrayList(createDefaultUser());
        }

        UCApi api = SpringBeanUtil.getBean(UCApi.class);
        UserPagedDTO userPagedDTO=new UserPagedDTO();
        userPagedDTO.setPageSize(1000);
        PagedResult<List<UserVO>> page = api.getByPage(userPagedDTO);
        if(page.getTotalRecords()<=0){
            return Collections.EMPTY_LIST;
        }
        List list=new ArrayList<UserEntityImpl>((int)page.getTotalRecords());
        page.getData().forEach(e->{
            list.add(convertToUser(e));
        });
        return list;
    }

    protected User findById(final String userId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication==null){

            return createDefaultUser();
        }

        UCApi api = SpringBeanUtil.getBean(UCApi.class);
        UserPagedDTO userPagedDTO=new UserPagedDTO();
        userPagedDTO.setName(userId);
        PagedResult<List<UserVO>> page = api.getByPage(userPagedDTO);
        if(page.getTotalRecords()<=0){
            return null;
        }
        return convertToUser(page.getData().get(0));
    }

    private User convertToUser(UserVO userVO) {
        UserEntityImpl userEntity=new UserEntityImpl();
        userEntity.setId(userVO.getName());
        userEntity.setLastName(userVO.getName());
        return null;
    }

    private User createDefaultUser(){
        UserEntityImpl userEntity=new UserEntityImpl();
        userEntity.setId("admin");
        userEntity.setLastName("test");
        return userEntity;
    }
}
