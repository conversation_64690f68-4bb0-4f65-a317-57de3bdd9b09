package com.swcares.aps.workflow.flowable.task;

import com.swcares.aps.workflow.flowable.service.SyncWorkflowTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @ClassName：SyncWorkflowScheduleTask
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/6/13 14:12
 * @version： v1.0
 */
@EnableScheduling
@Slf4j
@Component
public class SyncWorkflowScheduleTask {

    @Autowired
    private SyncWorkflowTaskService syncWorkflowTaskService;

    /**
     * 执行流程信息同步任务
     */
    @Scheduled(fixedDelay = 10000)
    public void syncSchedule() {
        log.info("--begin ---syncSchedule---");
        try {
            syncWorkflowTaskService.syncSchedule();
        }catch (Exception e){
            log.error("--error-- ---syncSchedule---");
        }
        log.info("--end-- ---syncSchedule---");
    }
}
