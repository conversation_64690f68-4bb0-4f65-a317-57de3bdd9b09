package com.swcares.aps.workflow.flowable.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.enums.AuditStatusEnum;
import com.swcares.aps.workflow.flowable.service.FlowableTaskService;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.common.util.TaskUtil;
import com.swcares.aps.workflow.flowable.service.FlowableVariableService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.task.api.Task;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * ClassName：FlowableTaskServiceImpl <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/10 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlowableTaskServiceImpl implements FlowableTaskService {

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private FlowableVariableService flowableVariableService;

    @Override
    public void completeTask(Task task,  CompleteProcessParamsDTO params) {

        String userId=params.getUserId();
        Map<String, Object> variable = ProcessParamsUtil.toMap(params, task.getTaskDefinitionKey());
        log.info("【aps-workflow-impl】FlowableTaskServiceImpl-taskId:[{}],传入params:[{}],completeTask方法-variable参数:{},startProcess:{}",task.getId(), JSONUtil.toJsonStr(params),JSONUtil.toJsonStr(variable));

        taskService.claim(task.getId(),null);
        taskService.claim(task.getId(),userId);

        taskService.setVariablesLocal(task.getId(),variable);

        String comment= TaskUtil.createTaskComment(params.getOptionCode(),params.getComment());
        taskService.addComment(task.getId(),task.getProcessInstanceId(),comment);
        log.info("【aps-workflow-impl】FlowableTaskServiceImpl-执行complete方法-taskId:[{}],传入params:[{}],completeTask方法-variable参数:{},startProcess:{}",task.getId(), JSONUtil.toJsonStr(params),JSONUtil.toJsonStr(variable));
        taskService.complete(task.getId(),variable);
    }

    @Override
    public void completeAutomaticTask(CompleteProcessParamsDTO params, List<String> automaticTaskIds) {

        for(String taskId:automaticTaskIds){
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            CompleteProcessParamsDTO copyParam= new CompleteProcessParamsDTO();
            BeanUtils.copyProperties(params,copyParam);
            copyParam.setComment("自动审核");
            copyParam.setOptionCode(AuditStatusEnum.AGREE.getKey());
            copyParam.setTaskId(task.getId());
            this.completeTask(task,copyParam);
        }
    }

    @Override
    public void completePushTask(CompleteProcessParamsDTO params, List<String> pushTaskIds) {
        for(String taskId:pushTaskIds){
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            CompleteProcessParamsDTO copyParam= new CompleteProcessParamsDTO();
            BeanUtils.copyProperties(params,copyParam);
            copyParam.setTaskId(task.getId());
            this.completeTask(task,copyParam);
        }
    }

    @Override
    public List<String> getAuditorPosition(String taskId) {
        List<String> assignees=new ArrayList<>();
        List<HistoricIdentityLink> identityLinks = historyService.getHistoricIdentityLinksForTask(taskId);
        if(CollectionUtils.isEmpty(identityLinks)){
            return Collections.EMPTY_LIST;
        }

        for(HistoricIdentityLink historicIdentityLink:identityLinks){
            String userId = historicIdentityLink.getUserId();
            if(StringUtils.isNotEmpty(userId)){
                assignees.add(userId);
            }
            String groupId = historicIdentityLink.getGroupId();
            if(StringUtils.isNotEmpty(groupId)){
                assignees.add(groupId);
            }
        }
        return assignees;
    }

    @Override
    public String getLastAssignee(Task task, HistoricProcessInstance historicProcessInstance) {
        String processInstanceId=historicProcessInstance.getId();
        Object processVariable = flowableVariableService.getProcessVariable(processInstanceId, ProcessParamsUtil.getTaskPreAssigneeParamName(task.getTaskDefinitionKey()));
        if(processVariable!=null){
            return String.valueOf(processVariable);
        }
        return null;
    }

    @Override
    public List<Task> getCurrentProcessTask(String processInstanceId) {
        return taskService.createTaskQuery().processInstanceId(processInstanceId).list();
    }


}
