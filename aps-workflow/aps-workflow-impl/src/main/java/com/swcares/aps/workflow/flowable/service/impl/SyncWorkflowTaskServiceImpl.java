package com.swcares.aps.workflow.flowable.service.impl;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO;
import com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper;
import com.swcares.aps.workflow.flowable.service.SyncWorkflowTaskService;
import com.swcares.aps.workflow.flowable.service.handler.SyncWorkflowTaskHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName：SyncWorkflowTaskServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/6/14 9:19
 * @version： v1.0
 */
@Service
@Slf4j
public class SyncWorkflowTaskServiceImpl implements SyncWorkflowTaskService {

    private final String SYNC_SCHEDULE_LOCK_KEY="WORKFLOW_SYNC_SCHEDULE_LOCK";

    @Autowired
    private SyncWorkflowTaskMapper syncWorkflowTaskMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private List<SyncWorkflowTaskHandlerService> syncWorkflowTaskHandlerServices;

    @Override
    public void syncSchedule() {
        RLock lock = redissonClient.getLock(SYNC_SCHEDULE_LOCK_KEY);

        List<SyncWorkflowTaskDO> taskLogDOS=null;
        try {
            boolean resLock = lock.tryLock(5, TimeUnit.SECONDS);
            if(resLock){
                taskLogDOS =takeTask();

                if(CollectionUtils.isNotEmpty(taskLogDOS)){

                    taskLogDOS.forEach(t-> {
                        t.setUpdatedTime(new Date());
                        t.setExecStatus(SyncWorkflowTaskDO.STATUS_ING);
                        t.setExecTimes(t.getExecTimes()+1);
                        syncWorkflowTaskMapper.updateById(t);
                    });
                }
            }

        } catch (Exception e) {
            log.error("syncSchedule 读取待执行任务出错",e);
        } finally {
            if(lock!=null && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }

        if(CollectionUtils.isEmpty(taskLogDOS)){ return; }
        for(SyncWorkflowTaskDO taskDO:taskLogDOS){
            syncSchedule(taskDO);
        }
    }

    private List<SyncWorkflowTaskDO> takeTask() {
        List<SyncWorkflowTaskDO> needSyncWorkflowTask = syncWorkflowTaskMapper.getNeedSyncWorkflowTask();
        if(CollectionUtils.isEmpty(needSyncWorkflowTask)){return needSyncWorkflowTask;}

        needSyncWorkflowTask = needSyncWorkflowTask.stream()
                .filter(t -> t.getExecTimes() >= 0
                        || DateUtil.between(t.getCreatedTime(),new Date(), DateUnit.SECOND)>30)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(needSyncWorkflowTask)){return needSyncWorkflowTask;}

        List<String> ids=new ArrayList<>(needSyncWorkflowTask.size());
        List<SyncWorkflowTaskDO> syncWorkflowTaskDOS=new ArrayList<>(needSyncWorkflowTask.size());
        for(SyncWorkflowTaskDO taskDO:needSyncWorkflowTask){
            if(ids.contains(taskDO.getOwnBusinessKey())){continue;}
            ids.add(taskDO.getOwnBusinessKey());
            syncWorkflowTaskDOS.add(taskDO);
        }
        return syncWorkflowTaskDOS;
    }

    void syncSchedule(SyncWorkflowTaskDO taskDO){
        for(SyncWorkflowTaskHandlerService service:syncWorkflowTaskHandlerServices){
            if(service.support(taskDO.getTaskType())){
                service.syncSchedule(taskDO);
            }
        }
    }
}
