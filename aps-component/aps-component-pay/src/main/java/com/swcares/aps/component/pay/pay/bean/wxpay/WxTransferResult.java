package com.swcares.aps.component.pay.pay.bean.wxpay;

import lombok.Data;


/**
 * ClassName：com.swcares.psi.service.wxpay.bean.WxTransferResult <br>
 * Description：微信企业支付-转账反馈参数 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Data
public class WxTransferResult {

    /**
     *  return_code : 返回状态码
     */
    private String return_code;

    /**
     *  return_msg : 返回信息
     */
    private String return_msg;

    /**
     *  mch_appid : 商户账号appid
     */
    private String mch_appid;

    /**
     *  mchid : 商户号
     */
    private String mchid;

    /**
     *  device_info : 设备号
     */
    private String device_info;

    /**
     *  nonce_str : 随机字符串
     */
    private String nonce_str;

    /**
     *  result_code : 业务结果
     */

    private String result_code;

    /**
     *  err_code : 错误代码
     */
    private String err_code;

    /**
     *  err_code_des : 错误代码描述
     */
    private String err_code_des;

    /**
     *  partner_trade_no : 商户订单号
     */
    private String partner_trade_no;

    /**
     *  payment_no : 微信订单号
     */
    private String payment_no;

    /**
     *  payment_time : 微信支付成功时间
     */
    private String payment_time;


}
